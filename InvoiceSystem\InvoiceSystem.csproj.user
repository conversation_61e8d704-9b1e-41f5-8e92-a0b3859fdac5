﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Compile Update="Controls\NewsTickerControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountingIntegrationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountingReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountMovementsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountsAgingReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountsOptionsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AccountStatementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AddChildAccountForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AddEditAccountForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\BalanceSheetReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\CashFlowReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\GeneralLedgerReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\IncomeStatementReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\SimplifiedChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\TrialBalanceReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\AccountsResetForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Customer\AddEditCustomerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\AddEditJournalEntryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\AddEditJournalEntryFormNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Invoices\AddPaymentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\AdvancedChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\ChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Customer\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\ExpenseCategoriesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\ExpenseNewAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\ExpenseNewManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\ExpensesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\AddEditExpenseCategoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Expenses\AddEditExpenseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\HierarchicalChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\JournalEntriesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\JournalEntriesManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\JournalEntriesManagementFormDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\JournalEntryDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\JournalEntries\JournalEntryDetailsViewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\ModernChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\News\NewsManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Notifications\EnhancedNotificationSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Notifications\NotificationDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Notifications\NotificationSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Notifications\NotificationsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Notifications\PopupNotificationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Partners\PartnerAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Partners\PartnerManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Partners\PartnerMovementsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Partners\PartnerViewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\AddEditProductCategoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\AddEditProductForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Invoices\CreateInvoiceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Invoices\InvoiceDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Invoices\InvoicesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\AddEditUnitOfMeasureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\ProductCategoriesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\ProductsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\UnitsOfMeasureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Reports\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Products\UpdateStockForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\SimpleChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\SecureMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\AddEditPermissionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\AddEditRoleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\AddEditUserForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\AddEditUserPermissionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\AuditLogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\ChangePasswordForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\ModernUserPermissionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\PermissionFixerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\PermissionManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\RoleManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\SecurityTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\SystemInitializationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\UserManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\UserPermissionDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Security\UserPermissionManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\SimpleInitializationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Testing\ApiClientTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Testing\TestDirectORMForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Testing\TrialBalanceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\AddEditVoucherForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\AddEditVoucherFormNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\VoucherDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\VoucherDetailsViewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\VouchersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\VouchersManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\TestChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\TestModernChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Accounting\TestSimpleChartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Vouchers\TestVouchersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="NewsTickerDemo.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>