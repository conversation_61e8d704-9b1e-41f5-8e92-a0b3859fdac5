using Dapper;
using InvoiceSystem.Data;
using InvoiceSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InvoiceSystem.Services
{
    /// <summary>
    /// خدمة إدارة حركات المنتجات
    /// </summary>
    public class ProductMovementService
    {
        private readonly DatabaseHelper _dbHelper;

        public ProductMovementService()
        {
            _dbHelper = new DatabaseHelper();
        }

        public ProductMovementService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        /// <summary>
        /// إضافة حركة منتج جديدة
        /// </summary>
        public async Task<int> AddMovementAsync(ProductMovement movement)
        {
            using var connection = _dbHelper.GetConnection();
            using var transaction = connection.BeginTransaction();

            try
            {
                movement.MovementDate = DateTime.Now;
                movement.TotalAmount = movement.Quantity * movement.UnitPrice;

                // إضافة الحركة
                var sql = @"INSERT INTO ProductMovements 
                           (ProductId, Type, Quantity, UnitPrice, TotalAmount, Reference, Notes, MovementDate, UserId)
                           VALUES (@ProductId, @Type, @Quantity, @UnitPrice, @TotalAmount, @Reference, @Notes, @MovementDate, @UserId);
                           SELECT last_insert_rowid();";

                var movementId = await connection.QuerySingleAsync<int>(sql, movement, transaction);

                // تحديث مخزون المنتج
                await UpdateProductStockAsync(connection, transaction, movement.ProductId, movement.Type, movement.Quantity);

                transaction.Commit();
                return movementId;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// تحديث مخزون المنتج حسب نوع الحركة
        /// </summary>
        private async Task UpdateProductStockAsync(System.Data.IDbConnection connection, System.Data.IDbTransaction transaction, 
            int productId, ProductMovementType type, int quantity)
        {
            string sql;
            switch (type)
            {
                case ProductMovementType.Sale:
                case ProductMovementType.PurchaseReturn:
                    // تقليل المخزون
                    sql = "UPDATE Products SET Stock = Stock - @Quantity WHERE Id = @ProductId";
                    break;
                case ProductMovementType.SaleReturn:
                case ProductMovementType.Purchase:
                    // زيادة المخزون
                    sql = "UPDATE Products SET Stock = Stock + @Quantity WHERE Id = @ProductId";
                    break;
                case ProductMovementType.StockAdjustment:
                    // تعديل المخزون (يمكن أن يكون موجب أو سالب)
                    sql = "UPDATE Products SET Stock = Stock + @Quantity WHERE Id = @ProductId";
                    break;
                default:
                    return; // لا تحديث للمخزون
            }

            await connection.ExecuteAsync(sql, new { Quantity = quantity, ProductId = productId }, transaction);
        }

        /// <summary>
        /// الحصول على حركات منتج معين
        /// </summary>
        public async Task<List<ProductMovement>> GetProductMovementsAsync(int productId)
        {
            using var connection = _dbHelper.GetConnection();
            var movements = await connection.QueryAsync<ProductMovement>(
                @"SELECT pm.*, p.Name as ProductName
                  FROM ProductMovements pm
                  LEFT JOIN Products p ON pm.ProductId = p.Id
                  WHERE pm.ProductId = @ProductId
                  ORDER BY pm.MovementDate DESC",
                new { ProductId = productId });
            return movements.ToList();
        }

        /// <summary>
        /// الحصول على جميع الحركات
        /// </summary>
        public async Task<List<ProductMovement>> GetAllMovementsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var movements = await connection.QueryAsync<ProductMovement>(
                @"SELECT pm.*, p.Name as ProductName
                  FROM ProductMovements pm
                  LEFT JOIN Products p ON pm.ProductId = p.Id
                  ORDER BY pm.MovementDate DESC");
            return movements.ToList();
        }

        /// <summary>
        /// الحصول على الحركات حسب النوع
        /// </summary>
        public async Task<List<ProductMovement>> GetMovementsByTypeAsync(ProductMovementType type)
        {
            using var connection = _dbHelper.GetConnection();
            var movements = await connection.QueryAsync<ProductMovement>(
                @"SELECT pm.*, p.Name as ProductName
                  FROM ProductMovements pm
                  LEFT JOIN Products p ON pm.ProductId = p.Id
                  WHERE pm.Type = @Type
                  ORDER BY pm.MovementDate DESC",
                new { Type = (int)type });
            return movements.ToList();
        }

        /// <summary>
        /// الحصول على الحركات في فترة زمنية
        /// </summary>
        public async Task<List<ProductMovement>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _dbHelper.GetConnection();
            var movements = await connection.QueryAsync<ProductMovement>(
                @"SELECT pm.*, p.Name as ProductName
                  FROM ProductMovements pm
                  LEFT JOIN Products p ON pm.ProductId = p.Id
                  WHERE pm.MovementDate BETWEEN @FromDate AND @ToDate
                  ORDER BY pm.MovementDate DESC",
                new { FromDate = fromDate, ToDate = toDate });
            return movements.ToList();
        }

        /// <summary>
        /// الحصول على إحصائيات المبيعات لمنتج
        /// </summary>
        public async Task<Dictionary<string, object>> GetProductSalesStatisticsAsync(int productId)
        {
            using var connection = _dbHelper.GetConnection();
            
            var salesData = await connection.QueryFirstOrDefaultAsync(
                @"SELECT 
                    SUM(CASE WHEN Type = 1 THEN Quantity ELSE 0 END) as TotalSold,
                    SUM(CASE WHEN Type = 2 THEN Quantity ELSE 0 END) as TotalReturned,
                    SUM(CASE WHEN Type = 1 THEN TotalAmount ELSE 0 END) as TotalSalesAmount,
                    SUM(CASE WHEN Type = 2 THEN TotalAmount ELSE 0 END) as TotalReturnAmount
                  FROM ProductMovements 
                  WHERE ProductId = @ProductId AND Type IN (1, 2)",
                new { ProductId = productId });

            return new Dictionary<string, object>
            {
                { "TotalSold", salesData?.TotalSold ?? 0 },
                { "TotalReturned", salesData?.TotalReturned ?? 0 },
                { "NetSold", (salesData?.TotalSold ?? 0) - (salesData?.TotalReturned ?? 0) },
                { "TotalSalesAmount", salesData?.TotalSalesAmount ?? 0 },
                { "TotalReturnAmount", salesData?.TotalReturnAmount ?? 0 },
                { "NetSalesAmount", (salesData?.TotalSalesAmount ?? 0) - (salesData?.TotalReturnAmount ?? 0) }
            };
        }

        /// <summary>
        /// الحصول على أفضل المنتجات مبيعاً
        /// </summary>
        public async Task<List<dynamic>> GetTopSellingProductsAsync(int topCount = 10)
        {
            using var connection = _dbHelper.GetConnection();
            var result = await connection.QueryAsync(
                @"SELECT p.Id, p.Name, 
                         SUM(CASE WHEN pm.Type = 1 THEN pm.Quantity ELSE 0 END) -
                         SUM(CASE WHEN pm.Type = 2 THEN pm.Quantity ELSE 0 END) as NetSold,
                         SUM(CASE WHEN pm.Type = 1 THEN pm.TotalAmount ELSE 0 END) -
                         SUM(CASE WHEN pm.Type = 2 THEN pm.TotalAmount ELSE 0 END) as NetAmount
                  FROM Products p
                  LEFT JOIN ProductMovements pm ON p.Id = pm.ProductId AND pm.Type IN (1, 2)
                  WHERE p.IsActive = 1
                  GROUP BY p.Id, p.Name
                  HAVING NetSold > 0
                  ORDER BY NetSold DESC
                  LIMIT @TopCount",
                new { TopCount = topCount });
            return result.ToList();
        }

        /// <summary>
        /// الحصول على تقرير المبيعات الشهري
        /// </summary>
        public async Task<List<dynamic>> GetMonthlySalesReportAsync(int year)
        {
            using var connection = _dbHelper.GetConnection();
            var result = await connection.QueryAsync(
                @"SELECT 
                    strftime('%m', MovementDate) as Month,
                    strftime('%Y', MovementDate) as Year,
                    SUM(CASE WHEN Type = 1 THEN Quantity ELSE 0 END) as SalesQuantity,
                    SUM(CASE WHEN Type = 2 THEN Quantity ELSE 0 END) as ReturnQuantity,
                    SUM(CASE WHEN Type = 1 THEN TotalAmount ELSE 0 END) as SalesAmount,
                    SUM(CASE WHEN Type = 2 THEN TotalAmount ELSE 0 END) as ReturnAmount
                  FROM ProductMovements
                  WHERE strftime('%Y', MovementDate) = @Year AND Type IN (1, 2)
                  GROUP BY strftime('%Y-%m', MovementDate)
                  ORDER BY Month",
                new { Year = year.ToString() });
            return result.ToList();
        }

        /// <summary>
        /// حذف حركة منتج
        /// </summary>
        public async Task<bool> DeleteMovementAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            using var transaction = connection.BeginTransaction();

            try
            {
                // الحصول على بيانات الحركة قبل الحذف
                var movement = await connection.QueryFirstOrDefaultAsync<ProductMovement>(
                    "SELECT * FROM ProductMovements WHERE Id = @Id", new { Id = id }, transaction);

                if (movement == null)
                    return false;

                // عكس تأثير الحركة على المخزون
                await ReverseStockUpdateAsync(connection, transaction, movement.ProductId, movement.Type, movement.Quantity);

                // حذف الحركة
                var rowsAffected = await connection.ExecuteAsync(
                    "DELETE FROM ProductMovements WHERE Id = @Id", new { Id = id }, transaction);

                transaction.Commit();
                return rowsAffected > 0;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// عكس تأثير الحركة على المخزون
        /// </summary>
        private async Task ReverseStockUpdateAsync(System.Data.IDbConnection connection, System.Data.IDbTransaction transaction,
            int productId, ProductMovementType type, int quantity)
        {
            string sql;
            switch (type)
            {
                case ProductMovementType.Sale:
                case ProductMovementType.PurchaseReturn:
                    // إرجاع المخزون (زيادة)
                    sql = "UPDATE Products SET Stock = Stock + @Quantity WHERE Id = @ProductId";
                    break;
                case ProductMovementType.SaleReturn:
                case ProductMovementType.Purchase:
                    // تقليل المخزون
                    sql = "UPDATE Products SET Stock = Stock - @Quantity WHERE Id = @ProductId";
                    break;
                case ProductMovementType.StockAdjustment:
                    // عكس التعديل
                    sql = "UPDATE Products SET Stock = Stock - @Quantity WHERE Id = @ProductId";
                    break;
                default:
                    return; // لا تحديث للمخزون
            }

            await connection.ExecuteAsync(sql, new { Quantity = quantity, ProductId = productId }, transaction);
        }
    }
}
