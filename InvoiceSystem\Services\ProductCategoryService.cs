using Dapper;
using InvoiceSystem.Data;
using InvoiceSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InvoiceSystem.Services
{
    /// <summary>
    /// خدمة إدارة تصنيفات المنتجات
    /// </summary>
    public class ProductCategoryService
    {
        private readonly DatabaseHelper _dbHelper;

        public ProductCategoryService()
        {
            _dbHelper = new DatabaseHelper();
        }

        public ProductCategoryService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        /// <summary>
        /// الحصول على جميع التصنيفات
        /// </summary>
        public async Task<List<ProductCategory>> GetAllCategoriesAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var categories = await connection.QueryAsync<ProductCategory>(
                "SELECT * FROM ProductCategories WHERE IsActive = 1 ORDER BY Name");
            return categories.ToList();
        }

        /// <summary>
        /// الحصول على تصنيف بالمعرف
        /// </summary>
        public async Task<ProductCategory?> GetCategoryByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            return await connection.QueryFirstOrDefaultAsync<ProductCategory>(
                "SELECT * FROM ProductCategories WHERE Id = @Id", new { Id = id });
        }

        /// <summary>
        /// إضافة تصنيف جديد
        /// </summary>
        public async Task<int> AddCategoryAsync(ProductCategory category)
        {
            using var connection = _dbHelper.GetConnection();
            category.CreatedDate = DateTime.Now;

            var sql = @"INSERT INTO ProductCategories (Name, Description, Color, IsActive, CreatedDate)
                       VALUES (@Name, @Description, @Color, @IsActive, @CreatedDate);
                       SELECT last_insert_rowid();";

            return await connection.QuerySingleAsync<int>(sql, category);
        }

        /// <summary>
        /// تحديث تصنيف
        /// </summary>
        public async Task<bool> UpdateCategoryAsync(ProductCategory category)
        {
            using var connection = _dbHelper.GetConnection();
            category.UpdatedDate = DateTime.Now;

            var sql = @"UPDATE ProductCategories
                       SET Name = @Name, Description = @Description, Color = @Color,
                           IsActive = @IsActive, UpdatedDate = @UpdatedDate
                       WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, category);
            return rowsAffected > 0;
        }

        /// <summary>
        /// حذف تصنيف
        /// </summary>
        public async Task<bool> DeleteCategoryAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();

            // التحقق من وجود منتجات مرتبطة
            var productCount = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE CategoryId = @Id", new { Id = id });

            if (productCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف التصنيف لوجود منتجات مرتبطة به");
            }

            var rowsAffected = await connection.ExecuteAsync(
                "DELETE FROM ProductCategories WHERE Id = @Id", new { Id = id });
            return rowsAffected > 0;
        }

        /// <summary>
        /// البحث في التصنيفات
        /// </summary>
        public async Task<List<ProductCategory>> SearchCategoriesAsync(string searchTerm)
        {
            using var connection = _dbHelper.GetConnection();
            var categories = await connection.QueryAsync<ProductCategory>(
                @"SELECT * FROM ProductCategories
                  WHERE IsActive = 1 AND (Name LIKE @SearchTerm OR Description LIKE @SearchTerm)
                  ORDER BY Name",
                new { SearchTerm = $"%{searchTerm}%" });
            return categories.ToList();
        }

        /// <summary>
        /// الحصول على إحصائيات التصنيفات
        /// </summary>
        public async Task<Dictionary<string, object>> GetCategoryStatisticsAsync()
        {
            using var connection = _dbHelper.GetConnection();

            var totalCategories = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM ProductCategories WHERE IsActive = 1");

            var categoriesWithProducts = await connection.QuerySingleAsync<int>(
                @"SELECT COUNT(DISTINCT CategoryId) FROM Products
                  WHERE CategoryId IS NOT NULL AND IsActive = 1");

            var categoriesWithoutProducts = totalCategories - categoriesWithProducts;

            return new Dictionary<string, object>
            {
                { "TotalCategories", totalCategories },
                { "CategoriesWithProducts", categoriesWithProducts },
                { "CategoriesWithoutProducts", categoriesWithoutProducts }
            };
        }

        /// <summary>
        /// الحصول على التصنيفات مع عدد المنتجات
        /// </summary>
        public async Task<List<dynamic>> GetCategoriesWithProductCountAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var result = await connection.QueryAsync(
                @"SELECT c.*,
                         COALESCE(p.ProductCount, 0) as ProductCount
                  FROM ProductCategories c
                  LEFT JOIN (
                      SELECT CategoryId, COUNT(*) as ProductCount
                      FROM Products
                      WHERE IsActive = 1
                      GROUP BY CategoryId
                  ) p ON c.Id = p.CategoryId
                  WHERE c.IsActive = 1
                  ORDER BY c.Name");
            return result.ToList();
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل تصنيف
        /// </summary>
        public async Task<bool> ToggleCategoryStatusAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();

            var currentStatus = await connection.QuerySingleAsync<bool>(
                "SELECT IsActive FROM ProductCategories WHERE Id = @Id", new { Id = id });

            var newStatus = !currentStatus;
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE ProductCategories SET IsActive = @IsActive, UpdatedDate = @UpdatedDate WHERE Id = @Id",
                new { IsActive = newStatus, UpdatedDate = DateTime.Now, Id = id });

            return rowsAffected > 0;
        }

        /// <summary>
        /// الحصول على التصنيفات النشطة فقط
        /// </summary>
        public async Task<List<ProductCategory>> GetActiveCategoriesAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var categories = await connection.QueryAsync<ProductCategory>(
                "SELECT * FROM ProductCategories WHERE IsActive = 1 ORDER BY Name");
            return categories.ToList();
        }

        /// <summary>
        /// التحقق من وجود تصنيف بنفس الاسم
        /// </summary>
        public async Task<bool> IsCategoryNameExistsAsync(string name, int? excludeId = null)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM ProductCategories WHERE Name = @Name";
            object parameters = new { Name = name };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                parameters = new { Name = name, ExcludeId = excludeId.Value };
            }

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count > 0;
        }
    }
}
