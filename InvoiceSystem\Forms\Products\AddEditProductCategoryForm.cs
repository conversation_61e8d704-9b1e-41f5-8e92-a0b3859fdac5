using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    /// <summary>
    /// نافذة إضافة/تعديل تصنيف المنتجات
    /// </summary>
    public partial class AddEditProductCategoryForm : Form
    {
        private readonly ProductCategoryService _categoryService;
        private readonly ProductCategory? _category;
        private readonly bool _isEditMode;

        public AddEditProductCategoryForm(ProductCategory? category = null)
        {
            InitializeComponent();
            _categoryService = new ProductCategoryService();
            _category = category;
            _isEditMode = category != null;

            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            if (_isEditMode)
            {
                this.Text = "تعديل تصنيف المنتجات";
                LoadCategoryData();
            }
            else
            {
                this.Text = "إضافة تصنيف جديد";
                SetDefaultValues();
            }

            SetupColorButtons();
        }

        private void LoadCategoryData()
        {
            if (_category != null)
            {
                txtName.Text = _category.Name;
                txtDescription.Text = _category.Description;
                chkIsActive.Checked = _category.IsActive;

                // تحديد اللون
                try
                {
                    var color = ColorTranslator.FromHtml(_category.Color);
                    SetSelectedColor(color);
                }
                catch
                {
                    SetSelectedColor(Color.FromArgb(0, 122, 204)); // اللون الافتراضي
                }
            }
        }

        private void SetDefaultValues()
        {
            chkIsActive.Checked = true;
            SetSelectedColor(Color.FromArgb(0, 122, 204)); // اللون الافتراضي
        }

        private void SetupColorButtons()
        {
            // إعداد أزرار الألوان المحددة مسبقاً
            var colors = new[]
            {
                Color.FromArgb(0, 122, 204),   // أزرق
                Color.FromArgb(255, 107, 53),  // برتقالي
                Color.FromArgb(247, 147, 30),  // أصفر برتقالي
                Color.FromArgb(139, 195, 74),  // أخضر
                Color.FromArgb(233, 30, 99),   // وردي
                Color.FromArgb(156, 39, 176),  // بنفسجي
                Color.FromArgb(63, 81, 181),   // أزرق داكن
                Color.FromArgb(0, 150, 136),   // أخضر مزرق
                Color.FromArgb(121, 85, 72),   // بني
                Color.FromArgb(76, 175, 80)    // أخضر فاتح
            };

            for (int i = 0; i < colors.Length && i < 10; i++)
            {
                var colorButton = this.Controls.Find($"btnColor{i + 1}", true)[0] as Button;
                if (colorButton != null)
                {
                    colorButton.BackColor = colors[i];
                    colorButton.Tag = ColorTranslator.ToHtml(colors[i]);
                    colorButton.Click += ColorButton_Click;
                }
            }
        }

        private void ColorButton_Click(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is string colorHtml)
            {
                var color = ColorTranslator.FromHtml(colorHtml);
                SetSelectedColor(color);
            }
        }

        private void SetSelectedColor(Color color)
        {
            panelSelectedColor.BackColor = color;
            lblSelectedColor.Text = ColorTranslator.ToHtml(color);

            // تحديث لون النص للتباين
            var brightness = (color.R * 299 + color.G * 587 + color.B * 114) / 1000;
            lblSelectedColor.ForeColor = brightness > 128 ? Color.Black : Color.White;
        }

        private void btnCustomColor_Click(object sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog();
            colorDialog.Color = panelSelectedColor.BackColor;
            colorDialog.FullOpen = true;

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                SetSelectedColor(colorDialog.Color);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم التصنيف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtName.Text.Length > 100)
            {
                MessageBox.Show("اسم التصنيف يجب أن يكون أقل من 100 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtDescription.Text.Length > 300)
            {
                MessageBox.Show("الوصف يجب أن يكون أقل من 300 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // التحقق من عدم تكرار الاسم
                var excludeId = _isEditMode ? _category?.Id : null;
                var nameExists = await _categoryService.IsCategoryNameExistsAsync(txtName.Text.Trim(), excludeId);

                if (nameExists)
                {
                    MessageBox.Show("يوجد تصنيف آخر بنفس الاسم", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                var category = new ProductCategory
                {
                    Name = txtName.Text.Trim(),
                    Description = txtDescription.Text.Trim(),
                    Color = lblSelectedColor.Text,
                    IsActive = chkIsActive.Checked
                };

                if (_isEditMode && _category != null)
                {
                    category.Id = _category.Id;
                    await _categoryService.UpdateCategoryAsync(category);
                    MessageBox.Show("تم تحديث التصنيف بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _categoryService.AddCategoryAsync(category);
                    MessageBox.Show("تم إضافة التصنيف بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التصنيف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtName_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الاسم
            lblPreviewName.Text = string.IsNullOrWhiteSpace(txtName.Text) ? "اسم التصنيف" : txtName.Text;
        }

        private void txtDescription_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الوصف
            lblPreviewDescription.Text = string.IsNullOrWhiteSpace(txtDescription.Text) ? "وصف التصنيف" : txtDescription.Text;
        }
    }

    partial class AddEditProductCategoryForm
    {
        private System.ComponentModel.IContainer components = null;
        private TextBox txtName;
        private TextBox txtDescription;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Label lblName;
        private Label lblDescription;
        private Label lblColor;
        private Panel panelSelectedColor;
        private Label lblSelectedColor;
        private Button btnCustomColor;
        private GroupBox groupBoxPreview;
        private Label lblPreviewName;
        private Label lblPreviewDescription;
        private Panel panelColorButtons;
        private Button btnColor1, btnColor2, btnColor3, btnColor4, btnColor5;
        private Button btnColor6, btnColor7, btnColor8, btnColor9, btnColor10;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.txtName = new TextBox();
            this.txtDescription = new TextBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblName = new Label();
            this.lblDescription = new Label();
            this.lblColor = new Label();
            this.panelSelectedColor = new Panel();
            this.lblSelectedColor = new Label();
            this.btnCustomColor = new Button();
            this.groupBoxPreview = new GroupBox();
            this.lblPreviewName = new Label();
            this.lblPreviewDescription = new Label();
            this.panelColorButtons = new Panel();
            this.btnColor1 = new Button();
            this.btnColor2 = new Button();
            this.btnColor3 = new Button();
            this.btnColor4 = new Button();
            this.btnColor5 = new Button();
            this.btnColor6 = new Button();
            this.btnColor7 = new Button();
            this.btnColor8 = new Button();
            this.btnColor9 = new Button();
            this.btnColor10 = new Button();

            this.groupBoxPreview.SuspendLayout();
            this.panelColorButtons.SuspendLayout();
            this.SuspendLayout();

            //
            // lblName
            //
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(12, 15);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(70, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "اسم التصنيف:";

            //
            // txtName
            //
            this.txtName.Location = new Point(12, 35);
            this.txtName.MaxLength = 100;
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(300, 23);
            this.txtName.TabIndex = 1;
            this.txtName.TextChanged += new EventHandler(this.txtName_TextChanged);

            //
            // lblDescription
            //
            this.lblDescription.AutoSize = true;
            this.lblDescription.Location = new Point(12, 70);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new Size(38, 15);
            this.lblDescription.TabIndex = 2;
            this.lblDescription.Text = "الوصف:";

            //
            // txtDescription
            //
            this.txtDescription.Location = new Point(12, 90);
            this.txtDescription.MaxLength = 300;
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.ScrollBars = ScrollBars.Vertical;
            this.txtDescription.Size = new Size(300, 80);
            this.txtDescription.TabIndex = 3;
            this.txtDescription.TextChanged += new EventHandler(this.txtDescription_TextChanged);

            //
            // lblColor
            //
            this.lblColor.AutoSize = true;
            this.lblColor.Location = new Point(12, 185);
            this.lblColor.Name = "lblColor";
            this.lblColor.Size = new Size(34, 15);
            this.lblColor.TabIndex = 4;
            this.lblColor.Text = "اللون:";

            //
            // panelColorButtons
            //
            this.panelColorButtons.Controls.Add(this.btnColor1);
            this.panelColorButtons.Controls.Add(this.btnColor2);
            this.panelColorButtons.Controls.Add(this.btnColor3);
            this.panelColorButtons.Controls.Add(this.btnColor4);
            this.panelColorButtons.Controls.Add(this.btnColor5);
            this.panelColorButtons.Controls.Add(this.btnColor6);
            this.panelColorButtons.Controls.Add(this.btnColor7);
            this.panelColorButtons.Controls.Add(this.btnColor8);
            this.panelColorButtons.Controls.Add(this.btnColor9);
            this.panelColorButtons.Controls.Add(this.btnColor10);
            this.panelColorButtons.Location = new Point(12, 205);
            this.panelColorButtons.Name = "panelColorButtons";
            this.panelColorButtons.Size = new Size(300, 60);
            this.panelColorButtons.TabIndex = 5;

            // إعداد أزرار الألوان
            var colorButtons = new[] { btnColor1, btnColor2, btnColor3, btnColor4, btnColor5, btnColor6, btnColor7, btnColor8, btnColor9, btnColor10 };
            for (int i = 0; i < colorButtons.Length; i++)
            {
                colorButtons[i].Location = new Point((i % 5) * 35, (i / 5) * 30);
                colorButtons[i].Name = $"btnColor{i + 1}";
                colorButtons[i].Size = new Size(30, 25);
                colorButtons[i].TabIndex = i;
                colorButtons[i].UseVisualStyleBackColor = false;
            }

            //
            // panelSelectedColor
            //
            this.panelSelectedColor.BorderStyle = BorderStyle.FixedSingle;
            this.panelSelectedColor.Controls.Add(this.lblSelectedColor);
            this.panelSelectedColor.Location = new Point(12, 275);
            this.panelSelectedColor.Name = "panelSelectedColor";
            this.panelSelectedColor.Size = new Size(200, 30);
            this.panelSelectedColor.TabIndex = 6;

            //
            // lblSelectedColor
            //
            this.lblSelectedColor.Dock = DockStyle.Fill;
            this.lblSelectedColor.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblSelectedColor.Location = new Point(0, 0);
            this.lblSelectedColor.Name = "lblSelectedColor";
            this.lblSelectedColor.Size = new Size(198, 28);
            this.lblSelectedColor.TabIndex = 0;
            this.lblSelectedColor.Text = "#007ACC";
            this.lblSelectedColor.TextAlign = ContentAlignment.MiddleCenter;

            //
            // btnCustomColor
            //
            this.btnCustomColor.Location = new Point(220, 275);
            this.btnCustomColor.Name = "btnCustomColor";
            this.btnCustomColor.Size = new Size(92, 30);
            this.btnCustomColor.TabIndex = 7;
            this.btnCustomColor.Text = "🎨 لون مخصص";
            this.btnCustomColor.UseVisualStyleBackColor = true;
            this.btnCustomColor.Click += new EventHandler(this.btnCustomColor_Click);

            //
            // chkIsActive
            //
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Checked = true;
            this.chkIsActive.CheckState = CheckState.Checked;
            this.chkIsActive.Location = new Point(12, 320);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Size = new Size(48, 19);
            this.chkIsActive.TabIndex = 8;
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.UseVisualStyleBackColor = true;

            //
            // groupBoxPreview
            //
            this.groupBoxPreview.Controls.Add(this.lblPreviewName);
            this.groupBoxPreview.Controls.Add(this.lblPreviewDescription);
            this.groupBoxPreview.Location = new Point(330, 35);
            this.groupBoxPreview.Name = "groupBoxPreview";
            this.groupBoxPreview.Size = new Size(250, 100);
            this.groupBoxPreview.TabIndex = 9;
            this.groupBoxPreview.TabStop = false;
            this.groupBoxPreview.Text = "معاينة";

            //
            // lblPreviewName
            //
            this.lblPreviewName.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPreviewName.Location = new Point(10, 20);
            this.lblPreviewName.Name = "lblPreviewName";
            this.lblPreviewName.Size = new Size(230, 25);
            this.lblPreviewName.TabIndex = 0;
            this.lblPreviewName.Text = "اسم التصنيف";

            //
            // lblPreviewDescription
            //
            this.lblPreviewDescription.Font = new Font("Segoe UI", 8F);
            this.lblPreviewDescription.ForeColor = Color.Gray;
            this.lblPreviewDescription.Location = new Point(10, 45);
            this.lblPreviewDescription.Name = "lblPreviewDescription";
            this.lblPreviewDescription.Size = new Size(230, 45);
            this.lblPreviewDescription.TabIndex = 1;
            this.lblPreviewDescription.Text = "وصف التصنيف";

            //
            // btnSave
            //
            this.btnSave.BackColor = Color.FromArgb(46, 204, 113);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(400, 315);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(90, 35);
            this.btnSave.TabIndex = 10;
            this.btnSave.Text = "💾 حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            //
            // btnCancel
            //
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(500, 315);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(90, 35);
            this.btnCancel.TabIndex = 11;
            this.btnCancel.Text = "❌ إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            //
            // AddEditProductCategoryForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 370);
            this.Controls.Add(this.lblName);
            this.Controls.Add(this.txtName);
            this.Controls.Add(this.lblDescription);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.lblColor);
            this.Controls.Add(this.panelColorButtons);
            this.Controls.Add(this.panelSelectedColor);
            this.Controls.Add(this.btnCustomColor);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.groupBoxPreview);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditProductCategoryForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة تصنيف جديد";

            this.groupBoxPreview.ResumeLayout(false);
            this.panelColorButtons.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
