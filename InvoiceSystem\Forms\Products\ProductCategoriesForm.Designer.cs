namespace InvoiceSystem.Forms.Products
{
    partial class ProductCategoriesForm
    {
        private System.ComponentModel.IContainer components = null;
        private DataGridView dgvCategories;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnToggleStatus;
        private Button btnRefresh;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblSearch;
        private Label lblStatus;
        private Panel panelTop;
        private Panel panelBottom;
        private Panel panelStats;
        private Label lblTotalCategories;
        private Label lblCategoriesWithProducts;
        private Label lblCategoriesWithoutProducts;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.dgvCategories = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnToggleStatus = new Button();
            this.btnRefresh = new Button();
            this.btnClose = new Button();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.lblStatus = new Label();
            this.panelTop = new Panel();
            this.panelBottom = new Panel();
            this.panelStats = new Panel();
            this.lblTotalCategories = new Label();
            this.lblCategoriesWithProducts = new Label();
            this.lblCategoriesWithoutProducts = new Label();

            ((System.ComponentModel.ISupportInitialize)(this.dgvCategories)).BeginInit();
            this.panelTop.SuspendLayout();
            this.panelBottom.SuspendLayout();
            this.panelStats.SuspendLayout();
            this.SuspendLayout();

            // 
            // dgvCategories
            // 
            this.dgvCategories.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) 
            | AnchorStyles.Left) 
            | AnchorStyles.Right)));
            this.dgvCategories.BackgroundColor = SystemColors.Window;
            this.dgvCategories.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCategories.Location = new Point(12, 80);
            this.dgvCategories.Name = "dgvCategories";
            this.dgvCategories.Size = new Size(960, 400);
            this.dgvCategories.TabIndex = 0;
            this.dgvCategories.DoubleClick += new EventHandler(this.dgvCategories_DoubleClick);

            // 
            // panelTop
            // 
            this.panelTop.Controls.Add(this.lblSearch);
            this.panelTop.Controls.Add(this.txtSearch);
            this.panelTop.Controls.Add(this.btnAdd);
            this.panelTop.Controls.Add(this.btnEdit);
            this.panelTop.Controls.Add(this.btnDelete);
            this.panelTop.Controls.Add(this.btnToggleStatus);
            this.panelTop.Controls.Add(this.btnRefresh);
            this.panelTop.Dock = DockStyle.Top;
            this.panelTop.Location = new Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new Size(984, 70);
            this.panelTop.TabIndex = 1;

            // 
            // lblSearch
            // 
            this.lblSearch.AutoSize = true;
            this.lblSearch.Location = new Point(12, 15);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new Size(38, 15);
            this.lblSearch.TabIndex = 0;
            this.lblSearch.Text = "البحث:";

            // 
            // txtSearch
            // 
            this.txtSearch.Location = new Point(56, 12);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.PlaceholderText = "البحث في التصنيفات...";
            this.txtSearch.Size = new Size(250, 23);
            this.txtSearch.TabIndex = 1;
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            // 
            // btnAdd
            // 
            this.btnAdd.BackColor = Color.FromArgb(52, 152, 219);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.Location = new Point(320, 10);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new Size(100, 30);
            this.btnAdd.TabIndex = 2;
            this.btnAdd.Text = "➕ إضافة";
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new EventHandler(this.btnAdd_Click);

            // 
            // btnEdit
            // 
            this.btnEdit.BackColor = Color.FromArgb(241, 196, 15);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(430, 10);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new Size(100, 30);
            this.btnEdit.TabIndex = 3;
            this.btnEdit.Text = "✏️ تعديل";
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            // 
            // btnDelete
            // 
            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(540, 10);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new Size(100, 30);
            this.btnDelete.TabIndex = 4;
            this.btnDelete.Text = "🗑️ حذف";
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            // 
            // btnToggleStatus
            // 
            this.btnToggleStatus.BackColor = Color.FromArgb(155, 89, 182);
            this.btnToggleStatus.ForeColor = Color.White;
            this.btnToggleStatus.Location = new Point(650, 10);
            this.btnToggleStatus.Name = "btnToggleStatus";
            this.btnToggleStatus.Size = new Size(100, 30);
            this.btnToggleStatus.TabIndex = 5;
            this.btnToggleStatus.Text = "🔄 تغيير الحالة";
            this.btnToggleStatus.UseVisualStyleBackColor = false;
            this.btnToggleStatus.Click += new EventHandler(this.btnToggleStatus_Click);

            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = Color.FromArgb(46, 204, 113);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(760, 10);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new Size(100, 30);
            this.btnRefresh.TabIndex = 6;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // 
            // panelStats
            // 
            this.panelStats.Controls.Add(this.lblTotalCategories);
            this.panelStats.Controls.Add(this.lblCategoriesWithProducts);
            this.panelStats.Controls.Add(this.lblCategoriesWithoutProducts);
            this.panelStats.Location = new Point(12, 45);
            this.panelStats.Name = "panelStats";
            this.panelStats.Size = new Size(960, 25);
            this.panelStats.TabIndex = 7;

            // 
            // lblTotalCategories
            // 
            this.lblTotalCategories.AutoSize = true;
            this.lblTotalCategories.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalCategories.ForeColor = Color.FromArgb(52, 152, 219);
            this.lblTotalCategories.Location = new Point(0, 5);
            this.lblTotalCategories.Name = "lblTotalCategories";
            this.lblTotalCategories.Size = new Size(100, 15);
            this.lblTotalCategories.TabIndex = 0;
            this.lblTotalCategories.Text = "إجمالي التصنيفات: 0";

            // 
            // lblCategoriesWithProducts
            // 
            this.lblCategoriesWithProducts.AutoSize = true;
            this.lblCategoriesWithProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCategoriesWithProducts.ForeColor = Color.FromArgb(46, 204, 113);
            this.lblCategoriesWithProducts.Location = new Point(150, 5);
            this.lblCategoriesWithProducts.Name = "lblCategoriesWithProducts";
            this.lblCategoriesWithProducts.Size = new Size(120, 15);
            this.lblCategoriesWithProducts.TabIndex = 1;
            this.lblCategoriesWithProducts.Text = "تصنيفات بها منتجات: 0";

            // 
            // lblCategoriesWithoutProducts
            // 
            this.lblCategoriesWithoutProducts.AutoSize = true;
            this.lblCategoriesWithoutProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCategoriesWithoutProducts.ForeColor = Color.FromArgb(231, 76, 60);
            this.lblCategoriesWithoutProducts.Location = new Point(320, 5);
            this.lblCategoriesWithoutProducts.Name = "lblCategoriesWithoutProducts";
            this.lblCategoriesWithoutProducts.Size = new Size(100, 15);
            this.lblCategoriesWithoutProducts.TabIndex = 2;
            this.lblCategoriesWithoutProducts.Text = "تصنيفات فارغة: 0";

            this.panelTop.Controls.Add(this.panelStats);

            // 
            // panelBottom
            // 
            this.panelBottom.Controls.Add(this.lblStatus);
            this.panelBottom.Controls.Add(this.btnClose);
            this.panelBottom.Dock = DockStyle.Bottom;
            this.panelBottom.Location = new Point(0, 490);
            this.panelBottom.Name = "panelBottom";
            this.panelBottom.Size = new Size(984, 50);
            this.panelBottom.TabIndex = 2;

            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new Point(12, 15);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(35, 15);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "جاهز";

            // 
            // btnClose
            // 
            this.btnClose.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.btnClose.BackColor = Color.FromArgb(149, 165, 166);
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(872, 10);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "🚪 إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            // 
            // ProductCategoriesForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(984, 540);
            this.Controls.Add(this.dgvCategories);
            this.Controls.Add(this.panelTop);
            this.Controls.Add(this.panelBottom);
            this.MinimumSize = new Size(800, 500);
            this.Name = "ProductCategoriesForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إدارة تصنيفات المنتجات";

            ((System.ComponentModel.ISupportInitialize)(this.dgvCategories)).EndInit();
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            this.panelBottom.ResumeLayout(false);
            this.panelBottom.PerformLayout();
            this.panelStats.ResumeLayout(false);
            this.panelStats.PerformLayout();
            this.ResumeLayout(false);
        }
    }
}
