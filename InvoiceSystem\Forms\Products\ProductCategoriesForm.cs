using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    /// <summary>
    /// نافذة إدارة تصنيفات المنتجات
    /// </summary>
    public partial class ProductCategoriesForm : Form
    {
        private readonly ProductCategoryService _categoryService;
        private List<ProductCategory> _categories;

        public ProductCategoriesForm()
        {
            InitializeComponent();
            _categoryService = new ProductCategoryService();
            _categories = new List<ProductCategory>();

            this.Text = "إدارة تصنيفات المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            SetupDataGridView();
            LoadCategoriesAsync();
        }

        private void SetupDataGridView()
        {
            dgvCategories.AutoGenerateColumns = false;
            dgvCategories.AllowUserToAddRows = false;
            dgvCategories.AllowUserToDeleteRows = false;
            dgvCategories.ReadOnly = true;
            dgvCategories.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCategories.MultiSelect = false;

            // إعداد الأعمدة
            dgvCategories.Columns.Clear();

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                HeaderText = "المعرف",
                Width = 60,
                Visible = false
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                DataPropertyName = "Name",
                HeaderText = "اسم التصنيف",
                Width = 200,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                DataPropertyName = "Description",
                HeaderText = "الوصف",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            // عمود اللون
            var colorColumn = new DataGridViewTextBoxColumn
            {
                Name = "Color",
                DataPropertyName = "Color",
                HeaderText = "اللون",
                Width = 80
            };
            dgvCategories.Columns.Add(colorColumn);

            dgvCategories.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Width = 60
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                DataPropertyName = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
            });

            // تلوين الخلايا حسب لون التصنيف
            dgvCategories.CellFormatting += DgvCategories_CellFormatting;
        }

        private void DgvCategories_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var category = dgvCategories.Rows[e.RowIndex].DataBoundItem as ProductCategory;
                if (category != null)
                {
                    // تلوين عمود اللون
                    if (dgvCategories.Columns[e.ColumnIndex].Name == "Color")
                    {
                        try
                        {
                            var color = ColorTranslator.FromHtml(category.Color);
                            e.CellStyle.BackColor = color;
                            e.CellStyle.ForeColor = GetContrastColor(color);
                        }
                        catch
                        {
                            e.CellStyle.BackColor = Color.White;
                            e.CellStyle.ForeColor = Color.Black;
                        }
                    }

                    // تمييز التصنيفات غير النشطة
                    if (!category.IsActive)
                    {
                        e.CellStyle.ForeColor = Color.Gray;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Italic);
                    }
                }
            }
        }

        private Color GetContrastColor(Color color)
        {
            // حساب اللون المتباين للنص
            var brightness = (color.R * 299 + color.G * 587 + color.B * 114) / 1000;
            return brightness > 128 ? Color.Black : Color.White;
        }

        private async void LoadCategoriesAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل التصنيفات...";
                lblStatus.ForeColor = Color.Blue;

                _categories = await _categoryService.GetAllCategoriesAsync();
                dgvCategories.DataSource = _categories;

                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_categories.Count} تصنيف";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في تحميل التصنيفات";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"خطأ في تحميل التصنيفات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _categoryService.GetCategoryStatisticsAsync();
                lblTotalCategories.Text = $"إجمالي التصنيفات: {stats["TotalCategories"]}";
                lblCategoriesWithProducts.Text = $"تصنيفات بها منتجات: {stats["CategoriesWithProducts"]}";
                lblCategoriesWithoutProducts.Text = $"تصنيفات فارغة: {stats["CategoriesWithoutProducts"]}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditProductCategoryForm();
            if (addForm.ShowDialog(this) == DialogResult.OK)
            {
                LoadCategoriesAsync();
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تصنيف للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedCategory = dgvCategories.SelectedRows[0].DataBoundItem as ProductCategory;
            if (selectedCategory != null)
            {
                var editForm = new AddEditProductCategoryForm(selectedCategory);
                if (editForm.ShowDialog(this) == DialogResult.OK)
                {
                    LoadCategoriesAsync();
                }
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تصنيف للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedCategory = dgvCategories.SelectedRows[0].DataBoundItem as ProductCategory;
            if (selectedCategory != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف التصنيف '{selectedCategory.Name}'؟\n\nملاحظة: لا يمكن حذف التصنيف إذا كان يحتوي على منتجات.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _categoryService.DeleteCategoryAsync(selectedCategory.Id);
                        MessageBox.Show("تم حذف التصنيف بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCategoriesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف التصنيف: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void btnToggleStatus_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تصنيف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedCategory = dgvCategories.SelectedRows[0].DataBoundItem as ProductCategory;
            if (selectedCategory != null)
            {
                try
                {
                    await _categoryService.ToggleCategoryStatusAsync(selectedCategory.Id);
                    var status = selectedCategory.IsActive ? "إلغاء تفعيل" : "تفعيل";
                    MessageBox.Show($"تم {status} التصنيف بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadCategoriesAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تغيير حالة التصنيف: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvCategories.DataSource = _categories;
            }
            else
            {
                try
                {
                    var searchResults = await _categoryService.SearchCategoriesAsync(txtSearch.Text);
                    dgvCategories.DataSource = searchResults;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error searching categories: {ex.Message}");
                }
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadCategoriesAsync();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dgvCategories_DoubleClick(object sender, EventArgs e)
        {
            btnEdit_Click(sender, e);
        }
    }
}
