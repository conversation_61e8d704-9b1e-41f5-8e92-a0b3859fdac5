using InvoiceSystem.Data;
using InvoiceSystem.Forms.Accounting;
using InvoiceSystem.Forms.Security;
using InvoiceSystem.Forms.Vouchers;
using InvoiceSystem.Forms.Notifications;
using InvoiceSystem.Forms.News;
using InvoiceSystem.Controls;
using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms
{
    public partial class SecureMainForm : Form
    {
        private readonly User _currentUser;
        private readonly string _sessionId;
        private readonly DatabaseHelper _dbHelper;
        private readonly UserService _userService;
        private readonly RoleService _roleService;
        private readonly PermissionService _permissionService;
        private readonly AuditService _auditService;
        private readonly SessionService _sessionService;
        private readonly AuthorizationService _authorizationService;
        private readonly DashboardService _dashboardService;
        private readonly NotificationService _notificationService;
        private readonly NotificationRuleEngine _notificationRuleEngine;
        private readonly NotificationScheduler _notificationScheduler;
        private readonly NotificationDatabaseService _notificationDatabaseService;
        private readonly PopupNotificationService _popupNotificationService;
        private readonly InvoiceNotificationService _invoiceNotificationService;
        private readonly EnhancedNotificationManager _enhancedNotificationManager;
        private readonly NotificationTestService _notificationTestService;
        private readonly NewsService _newsService;
        private NewsTickerControl _newsTicker;

        // Quick Action Buttons - Design Time
        private TableLayoutPanel quickActionsTableLayout;
        private Label quickActionsTitle;

        // Row 1 - Sales
        private Button btnQuickCustomers;
        private Button btnQuickProducts;
        private Button btnQuickInvoices;
        private Button btnQuickPayments;

        // Row 2 - Accounting
        private Button btnQuickAccounts;
        private Button btnQuickJournal;
        private Button btnQuickVouchers;
        private Button btnQuickTrialBalance;

        // Row 3 - Reports
        private Button btnQuickReports;
        private Button btnQuickAccountingReports;
        private Button btnQuickExpenses;
        private Button btnQuickPartners;

        // Row 4 - Notifications
        private Button btnQuickNotifications;
        private Button btnNotificationSettings;
        private Button btnForceNotificationCheck;
        private Button btnStopAllPopups;

        // Row 5 - Testing
        private Button btnTestNotifications;
        private Button btnTestPopups;
        private Button btnCleanupTestNotifications;

        // Row 6 - News Management
        private Button btnNewsManagement;
        private Button btnAddQuickNews;

        // Row 7 - Security
        private Button btnQuickUsers;
        private Button btnQuickRoles;
        private Button btnQuickUserPermissions;
        private Button btnQuickAudit;

        // Row 8 - Tools
        private Button btnQuickBackup;
        private Button btnQuickResetAccounts;
        private Button btnQuickApiTest;
        private Button btnQuickSimpleChart;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public User CurrentUser => _currentUser;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string SessionId => _sessionId;

        public SecureMainForm(User currentUser, string sessionId)
        {
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _sessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));

            InitializeComponent();

            // Initialize services
            _dbHelper = new DatabaseHelper();
            _auditService = new AuditService(_dbHelper);
            _userService = new UserService(_dbHelper, _auditService);
            _roleService = new RoleService(_dbHelper, _auditService);
            _permissionService = new PermissionService(_dbHelper, _auditService);
            _sessionService = new SessionService(_dbHelper);
            _authorizationService = new AuthorizationService(_dbHelper, _userService, _roleService);
            _dashboardService = new DashboardService(_dbHelper);

            // Initialize notification services
            _notificationDatabaseService = new NotificationDatabaseService(_dbHelper, _auditService);
            _notificationService = new NotificationService(_dbHelper, _auditService);
            _popupNotificationService = new PopupNotificationService(_notificationService, _auditService);
            _invoiceNotificationService = new InvoiceNotificationService(_dbHelper, _notificationService, _popupNotificationService, _auditService);
            _enhancedNotificationManager = new EnhancedNotificationManager(_dbHelper, _notificationService, _popupNotificationService, _invoiceNotificationService, _auditService);
            _notificationTestService = new NotificationTestService(_dbHelper, _enhancedNotificationManager, _notificationService, _auditService);
            _notificationRuleEngine = new NotificationRuleEngine(_dbHelper, _notificationService, _auditService);
            _notificationScheduler = new NotificationScheduler(_dbHelper, _notificationRuleEngine, _notificationService, _auditService);

            // Initialize news service
            _newsService = new NewsService(_dbHelper, _auditService);

            // ربط أحداث الإشعارات
            _notificationService.NotificationCreated += OnNotificationCreated;

            SetupForm();
            SetupDashboard();
            SetupNewsTicker();
        }

        private async void SecureMainForm_Load(object sender, EventArgs e)
        {
            await InitializeSecurityAsync();
            await LoadDashboardDataAsync();
            await StartNotificationServiceAsync();
            await StartNewsServiceAsync();
            await CreateWelcomeNotificationAsync();
            await LogUserActivityAsync("MainFormOpened", "User opened main application form");
        }

        private async void SetupForm()
        {
            // Set form title with user info
            this.Text = $"نظام إدارة الفواتير والمحاسبة - {_currentUser.FullName}";

            // Update welcome message
            welcomeLabel.Text = $"مرحباً بك، {_currentUser.FullName}";

            // Update user info
            userInfoLabel.Text = $"المستخدم: {_currentUser.FullName} | البريد: {_currentUser.Email}";
            userStatusLabel.Text = $"المستخدم: {_currentUser.Username}";

            // Update session info
            sessionInfoLabel.Text = $"🔐 معرف الجلسة: {_sessionId}";
            lastLoginLabel.Text = $"🕒 آخر تسجيل دخول: {_currentUser.LastLoginDate?.ToString("yyyy-MM-dd HH:mm") ?? "غير محدد"}";

            // Start timer for updates
            updateTimer.Start();
        }

        private void SetupDashboard()
        {
            try
            {
                // إنشاء عناصر تبويب الإحصائيات
                SetupStatsTab();

                // إنشاء عناصر تبويب العملاء
                SetupCustomersTab();

                // إنشاء عناصر تبويب الأنشطة
                SetupActivitiesTab();

                // إنشاء تبويب الأزرار السريعة
                SetupQuickActionsTab();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up dashboard: {ex.Message}");
            }
        }

        private void SetupStatsTab()
        {
            // إنشاء لوحات الإحصائيات
            var customersPanel = CreateStatsPanel("👥 إحصائيات العملاء", Color.FromArgb(52, 152, 219));
            var revenuePanel = CreateStatsPanel("💰 إحصائيات الإيرادات", Color.FromArgb(46, 204, 113));
            var expensesPanel = CreateStatsPanel("💸 إحصائيات المصروفات", Color.FromArgb(231, 76, 60));
            var partnersPanel = CreateStatsPanel("🤝 إحصائيات الشركاء", Color.FromArgb(155, 89, 182));
            var productsPanel = CreateStatsPanel("📦 إحصائيات المنتجات", Color.FromArgb(230, 126, 34));
            var performancePanel = CreateStatsPanel("📈 مؤشرات الأداء", Color.FromArgb(52, 73, 94));

            // إضافة العناصر إلى لوحات الإحصائيات
            AddCustomerStatsToPanel(customersPanel);
            AddRevenueStatsToPanel(revenuePanel);
            AddExpenseStatsToPanel(expensesPanel);
            AddPartnerStatsToPanel(partnersPanel);
            AddProductStatsToPanel(productsPanel);
            AddPerformanceStatsToPanel(performancePanel);

            // ترتيب اللوحات في الشبكة
            var tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // إعداد الأعمدة والصفوف
            for (int i = 0; i < 3; i++)
                tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33f));
            for (int i = 0; i < 2; i++)
                tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50f));

            // إضافة اللوحات
            tableLayout.Controls.Add(customersPanel, 0, 0);
            tableLayout.Controls.Add(revenuePanel, 1, 0);
            tableLayout.Controls.Add(expensesPanel, 2, 0);
            tableLayout.Controls.Add(partnersPanel, 0, 1);
            tableLayout.Controls.Add(productsPanel, 1, 1);
            tableLayout.Controls.Add(performancePanel, 2, 1);

            statsTabPage.Controls.Add(tableLayout);
        }

        private Panel CreateStatsPanel(string title, Color headerColor)
        {
            var panel = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                Margin = new Padding(5),
                Padding = new Padding(10)
            };

            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = headerColor
            };

            var titleLabel = new Label
            {
                Text = title,
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.Add(titleLabel);
            panel.Controls.Add(headerPanel);

            return panel;
        }

        private void AddCustomerStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblTotalCustomers = CreateStatsLabel("إجمالي العملاء: 0", 20);
            lblNewCustomers = CreateStatsLabel("عملاء جدد: 0", 50);
            lblCustomersBalance = CreateStatsLabel("أرصدة العملاء: 0.00", 80);

            var btnManageCustomers = CreateStatsButton("إدارة العملاء", 110, async (s, e) => await OpenCustomersFormAsync());

            contentPanel.Controls.AddRange(new Control[] { lblTotalCustomers, lblNewCustomers, lblCustomersBalance, btnManageCustomers });
            panel.Controls.Add(contentPanel);
        }

        private void AddRevenueStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblTotalRevenue = CreateStatsLabel("إجمالي الإيرادات: 0.00", 20);
            lblMonthlyRevenue = CreateStatsLabel("إيرادات الشهر: 0.00", 50);
            lblTodayRevenue = CreateStatsLabel("إيرادات اليوم: 0.00", 80);
            lblPendingAmount = CreateStatsLabel("مبالغ معلقة: 0.00", 110);

            var btnManageInvoices = CreateStatsButton("إدارة الفواتير", 140, async (s, e) => await OpenInvoicesFormAsync());

            contentPanel.Controls.AddRange(new Control[] { lblTotalRevenue, lblMonthlyRevenue, lblTodayRevenue, lblPendingAmount, btnManageInvoices });
            panel.Controls.Add(contentPanel);
        }

        private void AddExpenseStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblTotalExpenses = CreateStatsLabel("إجمالي المصروفات: 0.00", 20);
            lblMonthlyExpenses = CreateStatsLabel("مصروفات الشهر: 0.00", 50);
            lblTodayExpenses = CreateStatsLabel("مصروفات اليوم: 0.00", 80);
            lblNetProfit = CreateStatsLabel("صافي الربح: 0.00", 110);

            var btnManageExpenses = CreateStatsButton("إدارة المصروفات", 140, async (s, e) => await OpenExpensesAsync());

            contentPanel.Controls.AddRange(new Control[] { lblTotalExpenses, lblMonthlyExpenses, lblTodayExpenses, lblNetProfit, btnManageExpenses });
            panel.Controls.Add(contentPanel);
        }

        private void AddPartnerStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblTotalPartners = CreateStatsLabel("إجمالي الشركاء: 0", 20);
            lblActivePartners = CreateStatsLabel("شركاء نشطون: 0", 50);
            lblEmployees = CreateStatsLabel("موظفون: 0", 80);

            var btnManagePartners = CreateStatsButton("إدارة الشركاء", 110, async (s, e) => await OpenPartnersAsync());

            contentPanel.Controls.AddRange(new Control[] { lblTotalPartners, lblActivePartners, lblEmployees, btnManagePartners });
            panel.Controls.Add(contentPanel);
        }

        private void AddProductStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblTotalProducts = CreateStatsLabel("إجمالي المنتجات: 0", 20);
            lblOutOfStock = CreateStatsLabel("نفدت من المخزون: 0", 50);
            lblLowStock = CreateStatsLabel("مخزون منخفض: 0", 80);

            var btnManageProducts = CreateStatsButton("إدارة المنتجات", 110, async (s, e) => await OpenProductsFormAsync());

            contentPanel.Controls.AddRange(new Control[] { lblTotalProducts, lblOutOfStock, lblLowStock, btnManageProducts });
            panel.Controls.Add(contentPanel);
        }

        private void AddPerformanceStatsToPanel(Panel panel)
        {
            var contentPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10, 50, 10, 10) };

            lblCollectionRate = CreateStatsLabel("معدل التحصيل: 0%", 20);
            lblActivePercentage = CreateStatsLabel("نشاط الشركاء: 0%", 50);
            lblStockStatus = CreateStatsLabel("حالة المخزون: 0%", 80);

            var btnViewReports = CreateStatsButton("عرض التقارير", 110, async (s, e) => await OpenAccountingReportsAsync());

            contentPanel.Controls.AddRange(new Control[] { lblCollectionRate, lblActivePercentage, lblStockStatus, btnViewReports });
            panel.Controls.Add(contentPanel);
        }

        private Label CreateStatsLabel(string text, int top)
        {
            return new Label
            {
                Text = text,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, top),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private Button CreateStatsButton(string text, int top, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(52, 152, 219),
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, top),
                Size = new Size(180, 30),
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(41, 128, 185);
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(31, 97, 141);

            button.Click += clickHandler;

            return button;
        }

        private void SetupCustomersTab()
        {
            lstTopCustomers = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Segoe UI", 10F)
            };

            lstTopCustomers.Columns.Add("اسم العميل", 300);
            lstTopCustomers.Columns.Add("إجمالي المشتريات", 150);
            lstTopCustomers.Columns.Add("عدد الفواتير", 120);
            lstTopCustomers.Columns.Add("متوسط الفاتورة", 150);

            customersTabPage.Controls.Add(lstTopCustomers);
        }

        private void SetupActivitiesTab()
        {
            lstRecentActivities = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Segoe UI", 10F)
            };

            lstRecentActivities.Columns.Add("نوع النشاط", 150);
            lstRecentActivities.Columns.Add("الوصف", 400);
            lstRecentActivities.Columns.Add("المبلغ", 120);
            lstRecentActivities.Columns.Add("الوقت", 150);

            activitiesTabPage.Controls.Add(lstRecentActivities);
        }

        private void SetupQuickActionsTab()
        {
            // إنشاء الأزرار السريعة في ملف التصميم
            InitializeQuickActionsDesign();

            // ربط الأحداث
            SetupQuickActionEvents();
        }

        /// <summary>
        /// ربط أحداث الأزرار السريعة
        /// </summary>
        private void SetupQuickActionEvents()
        {
            // Row 1 - Sales
            btnQuickCustomers.Click += CustomersMenuItem_Click;
            btnQuickProducts.Click += ProductsMenuItem_Click;
            btnQuickInvoices.Click += InvoicesMenuItem_Click;
            btnQuickPayments.Click += async (s, e) => await OpenPaymentsAsync();

            // Row 2 - Accounting
            btnQuickAccounts.Click += ChartOfAccountsMenuItem_Click;
            btnQuickJournal.Click += JournalEntriesMenuItem_Click;
            btnQuickVouchers.Click += VouchersMenuItem_Click;
            btnQuickTrialBalance.Click += TrialBalanceMenuItem_Click;

            // Row 3 - Reports
            btnQuickReports.Click += SalesReportsMenuItem_Click;
            btnQuickAccountingReports.Click += AccountingReportsMenuItem_Click;
            btnQuickExpenses.Click += async (s, e) => await OpenExpensesAsync();
            btnQuickPartners.Click += async (s, e) => await OpenPartnersAsync();

            // Row 4 - Security
            btnQuickUsers.Click += UserManagementMenuItem_Click;
            btnQuickRoles.Click += RoleManagementMenuItem_Click;
            btnQuickUserPermissions.Click += async (s, e) => await OpenUserPermissionManagementAsync();
            btnQuickAudit.Click += AuditLogMenuItem_Click;

            // Row 5 - Tools
            btnQuickBackup.Click += async (s, e) => await CreateBackupAsync();
            btnQuickResetAccounts.Click += async (s, e) => await ResetAccountsAsync();
            btnQuickApiTest.Click += async (s, e) => await OpenApiTestAsync();
            btnQuickSimpleChart.Click += async (s, e) => await OpenSimpleChartAsync();

            // Notification button events
            btnQuickNotifications.Click += async (s, e) => await OpenNotificationsAsync();
            btnNotificationSettings.Click += async (s, e) => await OpenNotificationSettingsAsync();
            btnForceNotificationCheck.Click += async (s, e) => await ForceNotificationCheckAsync();
            btnStopAllPopups.Click += (s, e) => StopAllPopupsAsync();

            // Testing button events
            btnTestNotifications.Click += async (s, e) => await TestNotificationsAsync();
            btnTestPopups.Click += async (s, e) => await TestPopupsAsync();
            btnCleanupTestNotifications.Click += async (s, e) => await CleanupTestNotificationsAsync();

            // News Management button events
            btnNewsManagement.Click += async (s, e) => await OpenNewsManagementAsync();
            btnAddQuickNews.Click += async (s, e) => await AddQuickNewsAsync();

            // إضافة تأثيرات الحوم
            SetupButtonHoverEffects();
        }

        /// <summary>
        /// إعداد تأثيرات الحوم للأزرار
        /// </summary>
        private void SetupButtonHoverEffects()
        {
            var buttons = new[]
            {
                btnQuickCustomers, btnQuickProducts, btnQuickInvoices, btnQuickPayments,
                btnQuickAccounts, btnQuickJournal, btnQuickVouchers, btnQuickTrialBalance,
                btnQuickReports, btnQuickAccountingReports, btnQuickExpenses, btnQuickPartners,
                btnQuickUsers, btnQuickRoles, btnQuickUserPermissions, btnQuickAudit,
                btnQuickBackup, btnQuickResetAccounts, btnQuickApiTest, btnQuickSimpleChart,
                btnQuickNotifications, btnNotificationSettings, btnForceNotificationCheck, btnStopAllPopups,
                btnTestNotifications, btnTestPopups, btnCleanupTestNotifications,
                btnNewsManagement, btnAddQuickNews
            };

            foreach (var button in buttons)
            {
                var originalColor = button.BackColor;

                button.MouseEnter += (s, e) => {
                    button.BackColor = LightenColor(originalColor, 0.2f);
                    button.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                };

                button.MouseLeave += (s, e) => {
                    button.BackColor = originalColor;
                    button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                };
            }
        }





        /// <summary>
        /// Initialize Quick Actions Design Components
        /// </summary>
        private void InitializeQuickActionsDesign()
        {
            // Initialize components
            this.quickActionsTitle = new Label();
            this.quickActionsTableLayout = new TableLayoutPanel();

            // Row 1 - Sales
            this.btnQuickCustomers = new Button();
            this.btnQuickProducts = new Button();
            this.btnQuickInvoices = new Button();
            this.btnQuickPayments = new Button();

            // Row 2 - Accounting
            this.btnQuickAccounts = new Button();
            this.btnQuickJournal = new Button();
            this.btnQuickVouchers = new Button();
            this.btnQuickTrialBalance = new Button();

            // Row 3 - Reports
            this.btnQuickReports = new Button();
            this.btnQuickAccountingReports = new Button();
            this.btnQuickExpenses = new Button();
            this.btnQuickPartners = new Button();

            // Row 4 - Notifications
            this.btnQuickNotifications = new Button();
            this.btnNotificationSettings = new Button();
            this.btnForceNotificationCheck = new Button();
            this.btnStopAllPopups = new Button();

            // Row 5 - Testing
            this.btnTestNotifications = new Button();
            this.btnTestPopups = new Button();
            this.btnCleanupTestNotifications = new Button();

            // Row 6 - News Management
            this.btnNewsManagement = new Button();
            this.btnAddQuickNews = new Button();

            // Row 7 - Security
            this.btnQuickUsers = new Button();
            this.btnQuickRoles = new Button();
            this.btnQuickUserPermissions = new Button();
            this.btnQuickAudit = new Button();

            // Row 8 - Tools
            this.btnQuickBackup = new Button();
            this.btnQuickResetAccounts = new Button();
            this.btnQuickApiTest = new Button();
            this.btnQuickSimpleChart = new Button();

            this.quickActionsTableLayout.SuspendLayout();
            this.SuspendLayout();

            //
            // quickActionsTitle
            //
            this.quickActionsTitle.Dock = DockStyle.Top;
            this.quickActionsTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.quickActionsTitle.ForeColor = Color.FromArgb(52, 73, 94);
            this.quickActionsTitle.Location = new Point(20, 20);
            this.quickActionsTitle.Name = "quickActionsTitle";
            this.quickActionsTitle.Size = new Size(1110, 50);
            this.quickActionsTitle.TabIndex = 0;
            this.quickActionsTitle.Text = "⚡ الوصول السريع للوظائف الرئيسية";
            this.quickActionsTitle.TextAlign = ContentAlignment.MiddleCenter;

            //
            // quickActionsTableLayout
            //
            this.quickActionsTableLayout.ColumnCount = 4;
            this.quickActionsTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            this.quickActionsTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            this.quickActionsTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            this.quickActionsTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            this.quickActionsTableLayout.Controls.Add(this.btnQuickCustomers, 0, 0);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickProducts, 1, 0);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickInvoices, 2, 0);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickPayments, 3, 0);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickAccounts, 0, 1);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickJournal, 1, 1);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickVouchers, 2, 1);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickTrialBalance, 3, 1);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickReports, 0, 2);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickAccountingReports, 1, 2);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickExpenses, 2, 2);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickPartners, 3, 2);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickNotifications, 0, 3);
            this.quickActionsTableLayout.Controls.Add(this.btnNotificationSettings, 1, 3);
            this.quickActionsTableLayout.Controls.Add(this.btnForceNotificationCheck, 2, 3);
            this.quickActionsTableLayout.Controls.Add(this.btnStopAllPopups, 3, 3);
            this.quickActionsTableLayout.Controls.Add(this.btnTestNotifications, 0, 4);
            this.quickActionsTableLayout.Controls.Add(this.btnTestPopups, 1, 4);
            this.quickActionsTableLayout.Controls.Add(this.btnCleanupTestNotifications, 2, 4);
            this.quickActionsTableLayout.Controls.Add(this.btnNewsManagement, 0, 5);
            this.quickActionsTableLayout.Controls.Add(this.btnAddQuickNews, 1, 5);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickUsers, 0, 6);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickRoles, 1, 6);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickUserPermissions, 2, 6);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickAudit, 3, 6);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickBackup, 0, 7);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickResetAccounts, 1, 7);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickApiTest, 2, 7);
            this.quickActionsTableLayout.Controls.Add(this.btnQuickSimpleChart, 3, 7);
            this.quickActionsTableLayout.Dock = DockStyle.Fill;
            this.quickActionsTableLayout.Location = new Point(20, 70);
            this.quickActionsTableLayout.Name = "quickActionsTableLayout";
            this.quickActionsTableLayout.Padding = new Padding(20);
            this.quickActionsTableLayout.RowCount = 8;
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            this.quickActionsTableLayout.Size = new Size(1110, 511);
            this.quickActionsTableLayout.TabIndex = 1;

            // إضافة العناصر إلى التبويب
            this.quickActionsTabPage.Controls.Add(this.quickActionsTableLayout);
            this.quickActionsTabPage.Controls.Add(this.quickActionsTitle);

            // إعداد الأزرار
            SetupQuickActionButton(this.btnQuickCustomers, "👥 إدارة العملاء", "إضافة وتعديل بيانات العملاء", Color.FromArgb(52, 152, 219));
            SetupQuickActionButton(this.btnQuickProducts, "📦 إدارة المنتجات", "إضافة وتعديل المنتجات والمخزون", Color.FromArgb(46, 204, 113));
            SetupQuickActionButton(this.btnQuickInvoices, "📄 إدارة الفواتير", "إنشاء وإدارة فواتير المبيعات", Color.FromArgb(155, 89, 182));
            SetupQuickActionButton(this.btnQuickPayments, "💳 المدفوعات", "تسجيل وإدارة المدفوعات", Color.FromArgb(230, 126, 34));

            SetupQuickActionButton(this.btnQuickAccounts, "🌳 شجرة الحسابات", "إدارة دليل الحسابات المحاسبي", Color.FromArgb(52, 73, 94));
            SetupQuickActionButton(this.btnQuickJournal, "📝 القيود اليومية", "تسجيل القيود المحاسبية", Color.FromArgb(231, 76, 60));
            SetupQuickActionButton(this.btnQuickVouchers, "🧾 السندات", "سندات القبض والصرف", Color.FromArgb(26, 188, 156));
            SetupQuickActionButton(this.btnQuickTrialBalance, "⚖️ ميزان المراجعة", "عرض ميزان المراجعة", Color.FromArgb(142, 68, 173));

            SetupQuickActionButton(this.btnQuickReports, "📊 التقارير المالية", "تقارير المبيعات والمحاسبة", Color.FromArgb(39, 174, 96));
            SetupQuickActionButton(this.btnQuickAccountingReports, "📈 التقارير المحاسبية", "تقارير محاسبية متقدمة", Color.FromArgb(211, 84, 0));
            SetupQuickActionButton(this.btnQuickExpenses, "💸 إدارة المصروفات", "تسجيل وإدارة المصروفات", Color.FromArgb(192, 57, 43));
            SetupQuickActionButton(this.btnQuickPartners, "🤝 إدارة الشركاء", "إدارة الموظفين والشركاء", Color.FromArgb(41, 128, 185));

            // Row 4 - Notifications
            SetupQuickActionButton(this.btnQuickNotifications, "🔔 الإشعارات", "عرض وإدارة الإشعارات", Color.FromArgb(230, 126, 34));
            SetupQuickActionButton(this.btnNotificationSettings, "⚙️ إعدادات الإشعارات", "تخصيص إعدادات الإشعارات", Color.FromArgb(142, 68, 173));
            SetupQuickActionButton(this.btnForceNotificationCheck, "🔍 فحص فوري", "فحص فوري للإشعارات", Color.FromArgb(39, 174, 96));
            SetupQuickActionButton(this.btnStopAllPopups, "🚫 إيقاف النوافذ", "إيقاف جميع النوافذ المنبثقة", Color.FromArgb(231, 76, 60));

            // Row 5 - Testing
            SetupQuickActionButton(this.btnTestNotifications, "🧪 اختبار الإشعارات", "إنشاء إشعارات تجريبية شاملة", Color.FromArgb(155, 89, 182));
            SetupQuickActionButton(this.btnTestPopups, "🎭 اختبار النوافذ", "اختبار النوافذ المنبثقة", Color.FromArgb(52, 152, 219));
            SetupQuickActionButton(this.btnCleanupTestNotifications, "🧹 تنظيف الاختبارات", "حذف الإشعارات التجريبية", Color.FromArgb(192, 57, 43));

            // Row 6 - News Management
            SetupQuickActionButton(this.btnNewsManagement, "📰 إدارة الأخبار", "إدارة الأخبار والإعلانات", Color.FromArgb(255, 87, 34));
            SetupQuickActionButton(this.btnAddQuickNews, "➕ إضافة خبر", "إضافة خبر سريع للشريط", Color.FromArgb(76, 175, 80));

            SetupQuickActionButton(this.btnQuickUsers, "👤 إدارة المستخدمين", "إدارة مستخدمي النظام", Color.FromArgb(127, 140, 141));
            SetupQuickActionButton(this.btnQuickRoles, "🛡️ إدارة الأدوار", "إدارة أدوار وصلاحيات النظام", Color.FromArgb(44, 62, 80));
            SetupQuickActionButton(this.btnQuickUserPermissions, "🔐 صلاحيات المستخدمين", "إدارة صلاحيات المستخدمين المباشرة", Color.FromArgb(155, 89, 182));
            SetupQuickActionButton(this.btnQuickAudit, "📋 سجل الأحداث", "مراجعة سجل أحداث النظام", Color.FromArgb(149, 165, 166));

            SetupQuickActionButton(this.btnQuickBackup, "💾 النسخ الاحتياطي", "إنشاء نسخة احتياطية", Color.FromArgb(95, 39, 205));
            SetupQuickActionButton(this.btnQuickResetAccounts, "🔄 إعادة تعيين الحسابات", "إعادة تعيين الشجرة المحاسبية", Color.FromArgb(192, 57, 43));
            SetupQuickActionButton(this.btnQuickApiTest, "🧪 اختبار API", "اختبار Django API Client", Color.FromArgb(156, 39, 176));
            SetupQuickActionButton(this.btnQuickSimpleChart, "🌳 شجرة مبسطة", "الشجرة المحاسبية المبسطة", Color.FromArgb(76, 175, 80));

            this.quickActionsTableLayout.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        /// <summary>
        /// Setup Quick Action Button Properties
        /// </summary>
        private void SetupQuickActionButton(Button button, string title, string description, Color backgroundColor)
        {
            button.Dock = DockStyle.Fill;
            button.Margin = new Padding(10);
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleCenter;
            button.UseVisualStyleBackColor = false;
            button.Cursor = Cursors.Hand;
            button.Text = $"{title}\n\n{description}";

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = LightenColor(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = DarkenColor(backgroundColor, 0.1f);
        }

        /// <summary>
        /// تفتيح لون
        /// </summary>
        private static Color LightenColor(Color color, float factor)
        {
            int r = Math.Min(255, (int)(color.R + (255 - color.R) * factor));
            int g = Math.Min(255, (int)(color.G + (255 - color.G) * factor));
            int b = Math.Min(255, (int)(color.B + (255 - color.B) * factor));
            return Color.FromArgb(color.A, r, g, b);
        }

        /// <summary>
        /// تغميق لون
        /// </summary>
        private static Color DarkenColor(Color color, float factor)
        {
            int r = Math.Max(0, (int)(color.R * (1 - factor)));
            int g = Math.Max(0, (int)(color.G * (1 - factor)));
            int b = Math.Max(0, (int)(color.B * (1 - factor)));
            return Color.FromArgb(color.A, r, g, b);
        }

        // طرق مساعدة للوظائف الإضافية
        private async Task OpenPaymentsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenPayments", "User opened payments management");
                MessageBox.Show("ميزة إدارة المدفوعات قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المدفوعات", ex.Message);
            }
        }

        private async Task OpenExpensesAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenExpenses", "User opened expenses management");

                var expenseService = new ExpenseService(_dbHelper);
                var expensesForm = new ExpensesForm(expenseService);
                expensesForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المصروفات", ex.Message);
            }
        }

        private async Task OpenPartnersAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenPartners", "User opened partners management");

                var partnersForm = new PartnerManagementForm();
                partnersForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الشركاء", ex.Message);
            }
        }

        private async Task CreateBackupAsync()
        {
            try
            {
                await LogUserActivityAsync("CreateBackup", "User initiated backup creation");

                var result = MessageBox.Show("هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟",
                    "تأكيد النسخ الاحتياطي", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // TODO: تنفيذ النسخ الاحتياطي
                    MessageBox.Show("ميزة النسخ الاحتياطي قيد التطوير", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء النسخة الاحتياطية", ex.Message);
            }
        }

        private async Task ResetAccountsAsync()
        {
            try
            {
                await LogUserActivityAsync("ResetAccounts", "User initiated accounts reset");

                var resetForm = new AccountsResetForm();
                var result = resetForm.ShowDialog(this);

                if (result == DialogResult.OK && resetForm.ResetCompleted)
                {
                    MessageBox.Show("تم إعادة تعيين الشجرة المحاسبية بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إعادة تعيين الشجرة المحاسبية", ex.Message);
            }
        }



        private async Task InitializeSecurityAsync()
        {
            try
            {
                // Load user permissions
                var userPermissionNames = await _userService.GetUserPermissionsAsync(_currentUser.Id);
                var userRoles = await _userService.GetUserRolesAsync(_currentUser.Id);

                // Update permissions display
                var roleNames = string.Join(", ", userRoles.Select(r => r.DisplayName));
                var permissionCount = userPermissionNames.Count();

                permissionsInfoLabel.Text = $"🛡️ الأدوار: {roleNames}\n📋 عدد الصلاحيات: {permissionCount}";
                permissionsStatusLabel.Text = $"الصلاحيات: {permissionCount} صلاحية";

                // Apply security to UI elements
                await ApplySecurityToUIAsync(userPermissionNames);
            }
            catch (Exception ex)
            {
                permissionsInfoLabel.Text = $"❌ خطأ في تحميل الصلاحيات: {ex.Message}";
                permissionsStatusLabel.Text = "خطأ في الصلاحيات";

                await LogUserActivityAsync("SecurityError", $"Error loading permissions: {ex.Message}");
            }
        }

        private async Task ApplySecurityToUIAsync(System.Collections.Generic.IEnumerable<string> permissionNames)
        {
            var permissionSet = permissionNames.ToHashSet();

            // Sales menu permissions
            customersMenuItem.Enabled = customersToolButton.Enabled = quickCustomersBtn.Enabled =
                permissionSet.Contains(SystemPermissions.CustomersView);

            productsMenuItem.Enabled = productsToolButton.Enabled =
                permissionSet.Contains(SystemPermissions.ProductsView);

            invoicesMenuItem.Enabled = invoicesToolButton.Enabled = quickInvoicesBtn.Enabled =
                permissionSet.Contains(SystemPermissions.InvoicesView);

            // Accounting menu permissions
            chartOfAccountsMenuItem.Enabled = chartToolButton.Enabled = quickAccountingBtn.Enabled =
                permissionSet.Contains(SystemPermissions.AccountingView);

            journalEntriesMenuItem.Enabled = journalToolButton.Enabled =
                permissionSet.Contains(SystemPermissions.JournalEntriesView);

            vouchersMenuItem.Enabled = vouchersToolButton.Enabled =
                permissionSet.Contains(SystemPermissions.VouchersView);

            // Additional accounting permissions
            accountStatementsMenuItem.Enabled =
                permissionSet.Contains(SystemPermissions.AccountingView);

            trialBalanceMenuItem.Enabled = trialBalanceToolButton.Enabled = quickTrialBalanceBtn.Enabled =
                permissionSet.Contains(SystemPermissions.ReportsView);

            accountingReportsMenuItem.Enabled =
                permissionSet.Contains(SystemPermissions.ReportsView);

            // Reports permissions
            reportsMenu.Enabled = reportsToolButton.Enabled = quickReportsBtn.Enabled =
                permissionSet.Contains(SystemPermissions.ReportsView);

            // Security menu permissions
            var canManageUsers = permissionSet.Contains(SystemPermissions.UsersView);
            var canManageRoles = permissionSet.Contains(SystemPermissions.RolesView);
            var canManagePermissions = permissionSet.Contains(SystemPermissions.PermissionsView);
            var canManageUserPermissions = permissionSet.Contains(SystemPermissions.UsersView) || permissionSet.Contains(SystemPermissions.PermissionsView);
            var canViewAuditLog = permissionSet.Contains(SystemPermissions.SystemLogs);

            userManagementMenuItem.Enabled = canManageUsers;
            roleManagementMenuItem.Enabled = canManageRoles;
            permissionManagementMenuItem.Enabled = canManagePermissions;
            userPermissionManagementMenuItem.Enabled = canManageUserPermissions;
            auditLogMenuItem.Enabled = auditToolButton.Enabled = canViewAuditLog;

            // Hide security menu if no permissions
            securityMenu.Visible = securityToolButton.Visible = (canManageUsers || canManageRoles || canManagePermissions || canManageUserPermissions || canViewAuditLog);
        }

        private async Task LogUserActivityAsync(string action, string details)
        {
            try
            {
                await _auditService.LogAsync(
                    action: action,
                    entityType: "UserInterface",
                    entityId: null,
                    entityName: "MainForm",
                    oldValues: null,
                    newValues: new { Details = details, SessionId = _sessionId },
                    userId: _currentUser.Id,
                    module: "UI",
                    severity: AuditSeverity.Information,
                    success: true,
                    errorMessage: null);
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                Console.WriteLine($"Error logging user activity: {ex.Message}");
            }
        }

        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            // Update time display
            timeStatusLabel.Text = $"الوقت: {DateTime.Now:HH:mm:ss}";

            // Refresh dashboard data every 5 minutes
            if (DateTime.Now.Minute % 5 == 0 && DateTime.Now.Second == 0)
            {
                LoadDashboardDataAsync();
            }

            // Update notification count every 30 seconds
            if (DateTime.Now.Second % 30 == 0)
            {
                _ = Task.Run(UpdateNotificationCountAsync);
            }
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                var dashboardData = await _dashboardService.GetDashboardSummaryAsync();
                var topCustomers = await _dashboardService.GetTopCustomersAsync(5);
                var recentActivities = await _dashboardService.GetRecentActivitiesAsync(10);

                UpdateDashboardDisplay(dashboardData, topCustomers, recentActivities);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading dashboard data: {ex.Message}");
                await LogUserActivityAsync("DashboardError", $"Error loading dashboard: {ex.Message}");
            }
        }

        private void UpdateDashboardDisplay(DashboardSummary dashboard, List<TopCustomer> topCustomers, List<RecentActivity> recentActivities)
        {
            try
            {
                // تحديث إحصائيات العملاء
                lblTotalCustomers.Text = $"إجمالي العملاء: {dashboard.CustomerStats.TotalCustomers:N0}";
                lblNewCustomers.Text = $"عملاء جدد هذا الشهر: {dashboard.CustomerStats.NewCustomersThisMonth:N0}";
                lblCustomersBalance.Text = $"أرصدة العملاء: {dashboard.CustomerStats.TotalBalance:N2}";

                // تحديث إحصائيات الإيرادات
                lblTotalRevenue.Text = $"إجمالي الإيرادات: {dashboard.RevenueStats.TotalRevenue:N2}";
                lblMonthlyRevenue.Text = $"إيرادات الشهر: {dashboard.RevenueStats.MonthlyRevenue:N2}";
                lblTodayRevenue.Text = $"إيرادات اليوم: {dashboard.RevenueStats.TodayRevenue:N2}";
                lblPendingAmount.Text = $"مبالغ معلقة: {dashboard.RevenueStats.PendingAmount:N2}";

                // تحديث إحصائيات المصروفات
                lblTotalExpenses.Text = $"إجمالي المصروفات: {dashboard.ExpenseStats.TotalExpenses:N2}";
                lblMonthlyExpenses.Text = $"مصروفات الشهر: {dashboard.ExpenseStats.MonthlyExpenses:N2}";
                lblTodayExpenses.Text = $"مصروفات اليوم: {dashboard.ExpenseStats.TodayExpenses:N2}";

                // تحديث إحصائيات الشركاء
                lblTotalPartners.Text = $"إجمالي الشركاء: {dashboard.PartnerStats.TotalPartners:N0}";
                lblActivePartners.Text = $"شركاء نشطون: {dashboard.PartnerStats.ActivePartners:N0}";
                lblEmployees.Text = $"موظفون: {dashboard.PartnerStats.EmployeesCount:N0}";

                // تحديث إحصائيات المنتجات
                lblTotalProducts.Text = $"إجمالي المنتجات: {dashboard.ProductStats.TotalProducts:N0}";
                lblOutOfStock.Text = $"نفدت من المخزون: {dashboard.ProductStats.OutOfStock:N0}";
                lblLowStock.Text = $"مخزون منخفض: {dashboard.ProductStats.LowStock:N0}";

                // تحديث إحصائيات الفواتير
                lblTotalInvoices.Text = $"إجمالي الفواتير: {dashboard.InvoiceStats.TotalInvoices:N0}";
                lblMonthlyInvoices.Text = $"فواتير الشهر: {dashboard.InvoiceStats.MonthlyInvoices:N0}";
                lblTodayInvoices.Text = $"فواتير اليوم: {dashboard.InvoiceStats.TodayInvoices:N0}";

                // حساب صافي الربح
                var netProfit = dashboard.RevenueStats.TotalRevenue - dashboard.ExpenseStats.TotalExpenses;
                lblNetProfit.Text = $"صافي الربح: {netProfit:N2}";
                lblNetProfit.ForeColor = netProfit >= 0 ? Color.Green : Color.Red;

                // تحديث قائمة أفضل العملاء
                UpdateTopCustomersList(topCustomers);

                // تحديث قائمة الأنشطة الحديثة
                UpdateRecentActivitiesList(recentActivities);

                // تحديث مؤشرات الأداء
                UpdatePerformanceIndicators(dashboard);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating dashboard display: {ex.Message}");
            }
        }

        private void UpdateTopCustomersList(List<TopCustomer> topCustomers)
        {
            try
            {
                lstTopCustomers.Items.Clear();
                foreach (var customer in topCustomers)
                {
                    var item = new ListViewItem(customer.Name);
                    item.SubItems.Add(customer.TotalPurchases.ToString("N2"));
                    item.SubItems.Add(customer.InvoiceCount.ToString());
                    item.SubItems.Add(customer.AverageInvoiceAmount.ToString("N2"));
                    item.Tag = customer;
                    lstTopCustomers.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating top customers list: {ex.Message}");
            }
        }

        private void UpdateRecentActivitiesList(List<RecentActivity> recentActivities)
        {
            try
            {
                lstRecentActivities.Items.Clear();
                foreach (var activity in recentActivities)
                {
                    var item = new ListViewItem(activity.ActivityType);
                    item.SubItems.Add(activity.Description);
                    item.SubItems.Add(activity.Amount.ToString("N2"));
                    item.SubItems.Add(activity.TimeAgo);
                    item.Tag = activity;
                    lstRecentActivities.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating recent activities list: {ex.Message}");
            }
        }

        private void UpdatePerformanceIndicators(DashboardSummary dashboard)
        {
            try
            {
                // مؤشر معدل التحصيل
                var collectionRate = dashboard.RevenueStats.CollectionRate;
                lblCollectionRate.Text = $"معدل التحصيل: {collectionRate:F1}%";
                lblCollectionRate.ForeColor = collectionRate >= 80 ? Color.Green : collectionRate >= 60 ? Color.Orange : Color.Red;

                // مؤشر نشاط الشركاء
                var activePercentage = dashboard.PartnerStats.ActivePercentage;
                lblActivePercentage.Text = $"نشاط الشركاء: {activePercentage:F1}%";
                lblActivePercentage.ForeColor = activePercentage >= 80 ? Color.Green : activePercentage >= 60 ? Color.Orange : Color.Red;

                // مؤشر حالة المخزون
                var stockPercentage = dashboard.ProductStats.OutOfStockPercentage;
                lblStockStatus.Text = $"حالة المخزون: {(100 - stockPercentage):F1}%";
                lblStockStatus.ForeColor = stockPercentage <= 10 ? Color.Green : stockPercentage <= 25 ? Color.Orange : Color.Red;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating performance indicators: {ex.Message}");
            }
        }

        private async void SecureMainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // Stop notification service
                StopNotificationService();

                // Log user logout
                await LogUserActivityAsync("MainFormClosed", "User closed main application form");

                // End session
                await _sessionService.EndSessionAsync(_sessionId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during form closing: {ex.Message}");
            }
        }

        // Menu event handlers
        private void ExitMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private async void CustomersMenuItem_Click(object sender, EventArgs e)
        {
            await OpenCustomersFormAsync();
        }

        private async void ProductsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenProductsFormAsync();
        }

        private async void InvoicesMenuItem_Click(object sender, EventArgs e)
        {
            await OpenInvoicesFormAsync();
        }

        private async void ChartOfAccountsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenChartOfAccountsAsync();
        }

        private async void JournalEntriesMenuItem_Click(object sender, EventArgs e)
        {
            await OpenJournalEntriesAsync();
        }

        private async void VouchersMenuItem_Click(object sender, EventArgs e)
        {
            await OpenVouchersAsync();
        }

        private async void AccountStatementsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenAccountStatementsAsync();
        }

        private async void TrialBalanceMenuItem_Click(object sender, EventArgs e)
        {
            await OpenTrialBalanceAsync();
        }

        private async void AccountingReportsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenAccountingReportsAsync();
        }

        private async void SalesReportsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenReportsAsync();
        }

        private async void FinancialReportsMenuItem_Click(object sender, EventArgs e)
        {
            await OpenReportsAsync();
        }

        private async void UserManagementMenuItem_Click(object sender, EventArgs e)
        {
            await OpenUserManagementAsync();
        }

        private async void RoleManagementMenuItem_Click(object sender, EventArgs e)
        {
            await OpenRoleManagementAsync();
        }

        private async void PermissionManagementMenuItem_Click(object sender, EventArgs e)
        {
            await OpenPermissionManagementAsync();
        }

        private async void UserPermissionManagementMenuItem_Click(object sender, EventArgs e)
        {
            await OpenUserPermissionManagementAsync();
        }

        private async void PermissionFixerMenuItem_Click(object sender, EventArgs e)
        {
            await OpenPermissionFixerAsync();
        }

        private async void AuditLogMenuItem_Click(object sender, EventArgs e)
        {
            await OpenAuditLogAsync();
        }

        private async void ChangePasswordMenuItem_Click(object sender, EventArgs e)
        {
            await OpenChangePasswordAsync();
        }

        private void AboutMenuItem_Click(object sender, EventArgs e)
        {
            ShowAboutDialog();
        }

        // Toolbar event handlers
        private async void CustomersToolButton_Click(object sender, EventArgs e)
        {
            await OpenCustomersFormAsync();
        }

        private async void ProductsToolButton_Click(object sender, EventArgs e)
        {
            await OpenProductsFormAsync();
        }

        private async void InvoicesToolButton_Click(object sender, EventArgs e)
        {
            await OpenInvoicesFormAsync();
        }

        private async void ChartToolButton_Click(object sender, EventArgs e)
        {
            await OpenChartOfAccountsAsync();
        }

        private async void JournalToolButton_Click(object sender, EventArgs e)
        {
            await OpenJournalEntriesAsync();
        }

        private async void VouchersToolButton_Click(object sender, EventArgs e)
        {
            await OpenVouchersAsync();
        }

        private async void TrialBalanceToolButton_Click(object sender, EventArgs e)
        {
            await OpenTrialBalanceAsync();
        }

        private async void ReportsToolButton_Click(object sender, EventArgs e)
        {
            await OpenReportsAsync();
        }

        private async void SecurityToolButton_Click(object sender, EventArgs e)
        {
            await OpenUserManagementAsync();
        }

        private async void AuditToolButton_Click(object sender, EventArgs e)
        {
            await OpenAuditLogAsync();
        }

        // Quick access button handlers
        private async void QuickCustomersBtn_Click(object sender, EventArgs e)
        {
            await OpenCustomersFormAsync();
        }

        private async void QuickInvoicesBtn_Click(object sender, EventArgs e)
        {
            await OpenInvoicesFormAsync();
        }

        private async void QuickReportsBtn_Click(object sender, EventArgs e)
        {
            await OpenReportsAsync();
        }

        private async void QuickAccountingBtn_Click(object sender, EventArgs e)
        {
            await OpenChartOfAccountsAsync();
        }

        private async void QuickTrialBalanceBtn_Click(object sender, EventArgs e)
        {
            await OpenTrialBalanceAsync();
        }

        // Helper methods for opening forms
        private async Task OpenCustomersFormAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenCustomers", "User opened customers management form");

                var customerService = new CustomerService(_dbHelper);
                var customersForm = new CustomersForm(customerService);
                customersForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح نافذة العملاء", ex.Message);
                await LogUserActivityAsync("OpenCustomersError", $"Error opening customers form: {ex.Message}");
            }
        }

        private async Task OpenProductsFormAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenProducts", "User opened products management form");

                var productService = new ProductService(_dbHelper);
                var productsForm = new InvoiceSystem.Forms.Products.ProductsForm(productService);
                productsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح نافذة المنتجات", ex.Message);
                await LogUserActivityAsync("OpenProductsError", $"Error opening products form: {ex.Message}");
            }
        }

        private async Task UpdateDatabaseAsync()
        {
            try
            {
                await LogUserActivityAsync("UpdateDatabase", "User initiated database update");

                var updateForm = new DatabaseUpdateForm();
                if (updateForm.ShowDialog(this) == DialogResult.OK)
                {
                    ShowSuccessMessage("تم تحديث قاعدة البيانات بنجاح", "يمكنك الآن استخدام جميع الميزات الجديدة.");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحديث قاعدة البيانات", ex.Message);
                await LogUserActivityAsync("UpdateDatabaseError", $"Error updating database: {ex.Message}");
            }
        }

        private async Task OpenInvoicesFormAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenInvoices", "User opened invoices management form");

                var integrationService = new IntegrationService(_dbHelper);
                var customerService = new CustomerService(_dbHelper);
                var productService = new ProductService(_dbHelper);
                var invoiceService = integrationService.CreateIntegratedInvoiceService();
                var paymentService = integrationService.CreateIntegratedPaymentService();
                var invoicesForm = new InvoicesForm(invoiceService, customerService, productService, paymentService);
                invoicesForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح نافذة الفواتير", ex.Message);
                await LogUserActivityAsync("OpenInvoicesError", $"Error opening invoices form: {ex.Message}");
            }
        }

        private async Task OpenChartOfAccountsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenChartOfAccounts", "User opened chart of accounts form");

                var accountService = new AccountService(_dbHelper);

                // عرض قائمة خيارات شجرة الحسابات
                var optionsForm = new AccountsOptionsForm();
                var result = optionsForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    switch (optionsForm.SelectedOption)
                    {
                        case "Simple":
                            var simpleForm = new SimpleChartOfAccountsForm(accountService);
                            simpleForm.ShowDialog(this);
                            break;
                        case "Advanced":
                            var advancedForm = new AdvancedChartOfAccountsForm(accountService);
                            advancedForm.ShowDialog(this);
                            break;
                        case "Modern":
                            var modernForm = new ModernChartOfAccountsForm(accountService);
                            modernForm.ShowDialog(this);
                            break;
                        default:
                            var defaultForm = new ChartOfAccountsForm(accountService);
                            defaultForm.ShowDialog(this);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح شجرة الحسابات", ex.Message);
                await LogUserActivityAsync("OpenChartError", $"Error opening chart of accounts: {ex.Message}");
            }
        }

        private async Task OpenJournalEntriesAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenJournalEntries", "User opened journal entries form");

                var accountService = new AccountService(_dbHelper);
                var journalService = new JournalEntryService(_dbHelper, accountService);
                var journalForm = new JournalEntriesManagementFormDesigner(journalService, accountService);
                journalForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح القيود اليومية", ex.Message);
                await LogUserActivityAsync("OpenJournalError", $"Error opening journal entries: {ex.Message}");
            }
        }

        private async Task OpenVouchersAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenVouchers", "User opened vouchers management form");

                var vouchersManagementForm = new VouchersManagementForm();
                vouchersManagementForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح السندات", ex.Message);
                await LogUserActivityAsync("OpenVouchersError", $"Error opening vouchers: {ex.Message}");
            }
        }

        private async Task OpenReportsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenReports", "User opened reports form");

                var customerService = new CustomerService(_dbHelper);
                var productService = new ProductService(_dbHelper);
                var invoiceService = new InvoiceService(_dbHelper);
                var paymentService = new PaymentService(_dbHelper);
                var expenseService = new ExpenseService(_dbHelper);
                var reportsForm = new ReportsForm(customerService, productService, invoiceService, paymentService, expenseService);
                reportsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح التقارير", ex.Message);
                await LogUserActivityAsync("OpenReportsError", $"Error opening reports: {ex.Message}");
            }
        }

        private async Task OpenUserManagementAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenUserManagement", "User opened user management form");

                var userManagementForm = new UserManagementForm(_userService, _roleService, _permissionService, _auditService, _authorizationService, _currentUser);
                userManagementForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المستخدمين", ex.Message);
                await LogUserActivityAsync("OpenUserManagementError", $"Error opening user management: {ex.Message}");
            }
        }

        private async Task OpenRoleManagementAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenRoleManagement", "User opened role management form");

                var roleManagementForm = new RoleManagementForm(_roleService, _permissionService, _auditService, _currentUser);
                roleManagementForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الأدوار", ex.Message);
                await LogUserActivityAsync("OpenRoleManagementError", $"Error opening role management: {ex.Message}");
            }
        }

        private async Task OpenPermissionManagementAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenPermissionManagement", "User opened permission management form");

                var permissionManagementForm = new PermissionManagementForm(_permissionService, _roleService, _userService, _auditService, _currentUser);
                permissionManagementForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الصلاحيات", ex.Message);
                await LogUserActivityAsync("OpenPermissionManagementError", $"Error opening permission management: {ex.Message}");
            }
        }

        private async Task OpenUserPermissionManagementAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenUserPermissionManagement", "User opened user permission management form");

                // عرض خيار اختيار الواجهة
                var result = MessageBox.Show(
                    "أي واجهة تريد استخدامها؟\n\n" +
                    "نعم: الواجهة الحديثة (تبويبات + checkbox) 🆕\n" +
                    "لا: الواجهة التقليدية (جدول بيانات) 📊\n" +
                    "إلغاء: إلغاء العملية",
                    "اختيار واجهة إدارة الصلاحيات",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Cancel) return;

                var userPermissionService = new UserPermissionService(_dbHelper);

                if (result == DialogResult.Yes)
                {
                    // الواجهة الحديثة
                    var modernForm = new ModernUserPermissionForm(
                        userPermissionService,
                        _userService,
                        _permissionService,
                        _auditService,
                        _currentUser);

                    modernForm.ShowDialog(this);
                }
                else
                {
                    // الواجهة التقليدية
                    var traditionalForm = new UserPermissionManagementForm(
                        userPermissionService,
                        _userService,
                        _permissionService,
                        _auditService,
                        _currentUser);

                    traditionalForm.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة صلاحيات المستخدمين", ex.Message);
                await LogUserActivityAsync("OpenUserPermissionManagementError", $"Error opening user permission management: {ex.Message}");
            }
        }

        private async Task OpenPermissionFixerAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenPermissionFixer", "User opened permission fixer tool");

                var permissionFixerForm = new PermissionFixerForm();
                permissionFixerForm.ShowDialog(this);

                // Refresh permissions after fixing
                await InitializeSecurityAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح أداة إصلاح الصلاحيات", ex.Message);
                await LogUserActivityAsync("OpenPermissionFixerError", $"Error opening permission fixer: {ex.Message}");
            }
        }

        private async Task OpenAuditLogAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenAuditLog", "User opened audit log form");

                var auditLogForm = new AuditLogForm(_auditService, _userService, _currentUser);
                auditLogForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح سجل الأحداث", ex.Message);
                await LogUserActivityAsync("OpenAuditLogError", $"Error opening audit log: {ex.Message}");
            }
        }



        private async Task OpenChangePasswordAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenChangePassword", "User opened change password form");

                var changePasswordForm = new ChangePasswordForm(_userService, _auditService, _currentUser);
                var result = changePasswordForm.ShowDialog(this);

                if (result == DialogResult.OK && changePasswordForm.PasswordChanged)
                {
                    MessageBox.Show("تم تغيير كلمة المرور بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح نافذة تغيير كلمة المرور", ex.Message);
                await LogUserActivityAsync("OpenChangePasswordError", $"Error opening change password: {ex.Message}");
            }
        }

        private async Task OpenTrialBalanceAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenTrialBalance", "User opened trial balance form");

                var accountService = new AccountService(_dbHelper);
                var trialBalanceForm = new TrialBalanceForm(accountService);
                trialBalanceForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح ميزان المراجعة", ex.Message);
                await LogUserActivityAsync("OpenTrialBalanceError", $"Error opening trial balance: {ex.Message}");
            }
        }

        private async Task OpenAccountStatementsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenAccountStatements", "User opened account statements form");

                var accountService = new AccountService(_dbHelper);
                var accounts = await accountService.GetAllAccountsAsync();
                var accountSelectionForm = new AccountSelectionForm(accounts);
                var result = accountSelectionForm.ShowDialog(this);

                if (result == DialogResult.OK && accountSelectionForm.SelectedAccount != null)
                {
                    var movementsForm = new AccountMovementsForm(accountSelectionForm.SelectedAccount, accountService);
                    movementsForm.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح كشوف الحسابات", ex.Message);
                await LogUserActivityAsync("OpenAccountStatementsError", $"Error opening account statements: {ex.Message}");
            }
        }

        private async Task OpenAccountingReportsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenAccountingReports", "User opened accounting reports form");

                var accountService = new AccountService(_dbHelper);
                var reportsForm = new AccountingReportsForm(accountService);
                reportsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح التقارير المحاسبية", ex.Message);
                await LogUserActivityAsync("OpenAccountingReportsError", $"Error opening accounting reports: {ex.Message}");
            }
        }

        private void ShowAboutDialog()
        {
            var aboutMessage = @"نظام إدارة الفواتير والمحاسبة
الإصدار 2.0

نظام متكامل لإدارة:
• العملاء والمنتجات
• الفواتير والمبيعات
• المحاسبة والتقارير
• الأمان والصلاحيات

تم التطوير باستخدام:
• C# .NET 6
• Windows Forms
• SQLite Database
• Dapper ORM

© 2024 جميع الحقوق محفوظة";

            MessageBox.Show(aboutMessage, "حول البرنامج",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show($"{title}:\n\n{message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #region Notification Methods

        /// <summary>
        /// فتح نافذة الإشعارات
        /// </summary>
        private void NotificationsToolButton_Click(object sender, EventArgs e)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await LogUserActivityAsync("OpenNotifications", "User opened notifications window");

                    this.Invoke(new Action(() =>
                    {
                        var notificationsForm = new NotificationsForm(_notificationService, _currentUser);
                        notificationsForm.ShowDialog(this);
                    }));

                    // تحديث عدد الإشعارات بعد إغلاق النافذة
                    await UpdateNotificationCountAsync();
                }
                catch (Exception ex)
                {
                    this.Invoke(new Action(() =>
                    {
                        ShowErrorMessage("خطأ في فتح الإشعارات", ex.Message);
                    }));
                    await LogUserActivityAsync("OpenNotificationsError", $"Error opening notifications: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// النقر على شريط حالة الإشعارات
        /// </summary>
        private void NotificationsStatusLabel_Click(object sender, EventArgs e)
        {
            NotificationsToolButton_Click(sender, e);
        }

        /// <summary>
        /// تحديث عدد الإشعارات غير المقروءة
        /// </summary>
        private async Task UpdateNotificationCountAsync()
        {
            try
            {
                var unreadCount = await _notificationService.GetUnreadCountAsync(_currentUser?.Id);

                var statusText = unreadCount > 0
                    ? $"🔔 الإشعارات: {unreadCount}"
                    : "🔔 الإشعارات: 0";

                var buttonText = unreadCount > 0
                    ? $"🔔 الإشعارات ({unreadCount})"
                    : "🔔 الإشعارات";

                var textColor = unreadCount > 0 ? Color.Red : Color.Black;

                // تحديث UI في الـ UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        notificationsStatusLabel.ForeColor = textColor;
                        notificationsStatusLabel.Text = statusText;
                        notificationsToolButton.Text = buttonText;
                        notificationsToolButton.ForeColor = textColor;
                    }));
                }
                else
                {
                    notificationsStatusLabel.ForeColor = textColor;
                    notificationsStatusLabel.Text = statusText;
                    notificationsToolButton.Text = buttonText;
                    notificationsToolButton.ForeColor = textColor;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating notification count: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء خدمة الإشعارات
        /// </summary>
        private async Task StartNotificationServiceAsync()
        {
            try
            {
                // تهيئة قاعدة بيانات الإشعارات
                await InitializeNotificationDatabaseAsync();

                // بدء خدمة جدولة الإشعارات
                _notificationScheduler.Start();

                // تحديث عدد الإشعارات الأولي
                await UpdateNotificationCountAsync();

                await LogUserActivityAsync("StartNotificationService", "Notification service started successfully");
            }
            catch (Exception ex)
            {
                await LogUserActivityAsync("StartNotificationServiceError", $"Error starting notification service: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة قاعدة بيانات الإشعارات
        /// </summary>
        private async Task InitializeNotificationDatabaseAsync()
        {
            try
            {
                // تهيئة نظام الإشعارات بالكامل
                await NotificationInitializer.InitializeNotificationSystemAsync(_dbHelper, _auditService);

                // التحقق من صحة النظام
                var isValid = await NotificationInitializer.ValidateNotificationSystemAsync(_dbHelper, _auditService);

                if (!isValid)
                {
                    throw new Exception("فشل في التحقق من صحة نظام الإشعارات");
                }

                await LogUserActivityAsync("InitializeNotificationDatabase", "Notification database initialized successfully");
            }
            catch (Exception ex)
            {
                await LogUserActivityAsync("InitializeNotificationDatabaseError", $"Error initializing notification database: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة الإشعارات
        /// </summary>
        private void StopNotificationService()
        {
            try
            {
                _notificationScheduler?.Stop();
                _notificationScheduler?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stopping notification service: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث إنشاء إشعار جديد
        /// </summary>
        private async void OnNotificationCreated(object sender, Notification notification)
        {
            try
            {
                // تحديث عدد الإشعارات
                await UpdateNotificationCountAsync();

                // عرض الإشعار المنبثق إذا كان مناسباً
                if (_currentUser != null && notification.Priority >= NotificationPriority.Normal)
                {
                    await _popupNotificationService.ShowPopupNotificationAsync(notification, _currentUser.Id);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error handling notification created event: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء إشعار ترحيب للمستخدم
        /// </summary>
        private async Task CreateWelcomeNotificationAsync()
        {
            try
            {
                await _notificationService.CreateNotificationAsync(
                    title: "مرحباً بك في النظام",
                    message: $"مرحباً {_currentUser.FullName}، تم تسجيل دخولك بنجاح إلى نظام إدارة الفواتير والمحاسبة.",
                    type: NotificationType.Success,
                    priority: NotificationPriority.Normal,
                    category: NotificationCategory.System,
                    userId: _currentUser.Id,
                    expiryDate: DateTime.Now.AddDays(1),
                    showPopup: true
                );

                await UpdateNotificationCountAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating welcome notification: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء إشعارات تجريبية للاختبار
        /// </summary>
        private async Task CreateTestNotificationsAsync()
        {
            try
            {
                await NotificationInitializer.CreateTestNotificationsAsync(_dbHelper, _auditService, _currentUser?.Id);
                await UpdateNotificationCountAsync();

                MessageBox.Show("تم إنشاء الإشعارات التجريبية بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء الإشعارات التجريبية", ex.Message);
            }
        }

        /// <summary>
        /// معالج حدث زر اختبار الإشعارات
        /// </summary>
        private void TestNotificationsToolButton_Click(object sender, EventArgs e)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await CreateTestNotificationsAsync();
                }
                catch (Exception ex)
                {
                    this.Invoke(new Action(() =>
                    {
                        ShowErrorMessage("خطأ في إنشاء الإشعارات التجريبية", ex.Message);
                    }));
                }
            });
        }



        /// <summary>
        /// فتح نافذة اختبار API
        /// </summary>
        private async Task OpenApiTestAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenApiTest", "User opened API test form");

                var apiTestForm = new Forms.Testing.ApiClientTestForm();
                apiTestForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح نافذة اختبار API", ex.Message);
                await LogUserActivityAsync("OpenApiTestError", $"Error opening API test form: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح الشجرة المحاسبية المبسطة
        /// </summary>
        private async Task OpenSimpleChartAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenSimpleChart", "User opened simplified chart of accounts");

                var accountService = new AccountService(_dbHelper);
                var simpleChartForm = new Forms.Accounting.SimplifiedChartOfAccountsForm(accountService);
                simpleChartForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح الشجرة المحاسبية المبسطة", ex.Message);
                await LogUserActivityAsync("OpenSimpleChartError", $"Error opening simplified chart: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة الإشعارات المحسنة
        /// </summary>
        private async Task OpenNotificationsAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenEnhancedNotifications", "User opened enhanced notifications window");

                var notificationsForm = new NotificationsForm(_notificationService, _currentUser);
                notificationsForm.ShowDialog(this);

                // تحديث عدد الإشعارات بعد إغلاق النافذة
                await UpdateNotificationCountAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح الإشعارات", ex.Message);
                await LogUserActivityAsync("OpenEnhancedNotificationsError", $"Error opening enhanced notifications: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة إعدادات الإشعارات المحسنة
        /// </summary>
        private async Task OpenNotificationSettingsAsync()
        {
            try
            {
                if (_currentUser == null)
                {
                    MessageBox.Show("يجب تسجيل الدخول لتعديل الإعدادات", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                await LogUserActivityAsync("OpenEnhancedNotificationSettings", "User opened enhanced notification settings");

                var settingsForm = new EnhancedNotificationSettingsForm(_notificationService, _enhancedNotificationManager, _currentUser);
                if (settingsForm.ShowDialog(this) == DialogResult.OK)
                {
                    MessageBox.Show("تم حفظ إعدادات الإشعارات بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إعدادات الإشعارات المحسنة", ex.Message);
                await LogUserActivityAsync("OpenEnhancedNotificationSettingsError", $"Error opening enhanced notification settings: {ex.Message}");
            }
        }

        /// <summary>
        /// تنفيذ فحص فوري للإشعارات
        /// </summary>
        private async Task ForceNotificationCheckAsync()
        {
            try
            {
                await LogUserActivityAsync("ForceNotificationCheck", "User initiated force notification check");

                await _enhancedNotificationManager.ForceCheckAsync();

                await _enhancedNotificationManager.CreateCustomNotificationAsync(
                    title: "فحص الإشعارات",
                    message: "تم تنفيذ فحص فوري للإشعارات بنجاح في " + DateTime.Now.ToString("HH:mm:ss"),
                    type: NotificationType.Success,
                    priority: NotificationPriority.Normal,
                    category: NotificationCategory.System,
                    userId: _currentUser?.Id,
                    showPopup: true
                );

                await UpdateNotificationCountAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تنفيذ الفحص الفوري للإشعارات", ex.Message);
                await LogUserActivityAsync("ForceNotificationCheckError", $"Error in force notification check: {ex.Message}");
            }
        }

        /// <summary>
        /// إيقاف جميع النوافذ المنبثقة
        /// </summary>
        private void StopAllPopupsAsync()
        {
            try
            {
                _enhancedNotificationManager.StopAllPopups();

                MessageBox.Show("تم إيقاف جميع النوافذ المنبثقة!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                _ = LogUserActivityAsync("StopAllPopups", "User stopped all popup notifications");
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إيقاف النوافذ المنبثقة", ex.Message);
                _ = LogUserActivityAsync("StopAllPopupsError", $"Error stopping popup notifications: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار الإشعارات الشامل
        /// </summary>
        private async Task TestNotificationsAsync()
        {
            try
            {
                await LogUserActivityAsync("TestNotifications", "User initiated comprehensive notification test");

                var result = MessageBox.Show(
                    "هل تريد إنشاء إشعارات تجريبية شاملة؟\nسيتم إنشاء إشعارات من جميع الأنواع والفئات.",
                    "اختبار الإشعارات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _notificationTestService.CreateComprehensiveTestNotificationsAsync(_currentUser?.Id);

                    MessageBox.Show("تم إنشاء الإشعارات التجريبية بنجاح!\nتحقق من قائمة الإشعارات لرؤية النتائج.",
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    await UpdateNotificationCountAsync();
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في اختبار الإشعارات", ex.Message);
                await LogUserActivityAsync("TestNotificationsError", $"Error in notification test: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار النوافذ المنبثقة
        /// </summary>
        private async Task TestPopupsAsync()
        {
            try
            {
                await LogUserActivityAsync("TestPopups", "User initiated popup notification test");

                var result = MessageBox.Show(
                    "هل تريد اختبار النوافذ المنبثقة؟\nسيتم عرض 5 نوافذ منبثقة من أنواع مختلفة.",
                    "اختبار النوافذ المنبثقة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _notificationTestService.TestPopupNotificationsAsync(_currentUser?.Id);

                    MessageBox.Show("تم بدء اختبار النوافذ المنبثقة!\nستظهر النوافذ تدريجياً خلال الثواني القادمة.",
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في اختبار النوافذ المنبثقة", ex.Message);
                await LogUserActivityAsync("TestPopupsError", $"Error in popup test: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الإشعارات التجريبية
        /// </summary>
        private async Task CleanupTestNotificationsAsync()
        {
            try
            {
                await LogUserActivityAsync("CleanupTestNotifications", "User initiated test notifications cleanup");

                var result = MessageBox.Show(
                    "هل تريد حذف جميع الإشعارات التجريبية؟\nسيتم حذف جميع الإشعارات التي تحتوي على كلمات 'اختبار' أو 'تجريبي'.",
                    "تنظيف الإشعارات التجريبية",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await _notificationTestService.CleanupTestNotificationsAsync();

                    MessageBox.Show("تم حذف الإشعارات التجريبية بنجاح!",
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    await UpdateNotificationCountAsync();
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تنظيف الإشعارات التجريبية", ex.Message);
                await LogUserActivityAsync("CleanupTestNotificationsError", $"Error in cleanup test notifications: {ex.Message}");
            }
        }

        #endregion

        #region News Ticker Management

        /// <summary>
        /// إعداد الشريط الإخباري
        /// </summary>
        private void SetupNewsTicker()
        {
            try
            {
                _newsTicker = new NewsTickerControl(_newsService)
                {
                    Dock = DockStyle.Bottom,
                    Height = 35,
                    ScrollSpeed = 2,
                    RefreshInterval = 30000, // 30 ثانية
                    AutoRefresh = true,
                    ShowIcons = true,
                    EnableSound = false
                };

                // ربط الأحداث
                _newsTicker.NewsItemClicked += NewsTicker_NewsItemClicked;
                _newsTicker.NewsItemDisplayed += NewsTicker_NewsItemDisplayed;
                _newsTicker.TickerStarted += NewsTicker_TickerStarted;
                _newsTicker.TickerStopped += NewsTicker_TickerStopped;

                // إضافة الشريط إلى النموذج
                this.Controls.Add(_newsTicker);
                _newsTicker.BringToFront();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up news ticker: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء خدمة الأخبار
        /// </summary>
        private async Task StartNewsServiceAsync()
        {
            try
            {
                await _newsService.InitializeNewsTablesAsync();
                await CreateSampleNewsAsync();
                await _newsTicker.StartAsync();

                await LogUserActivityAsync("NewsServiceStarted", "News service started successfully");
            }
            catch (Exception ex)
            {
                await LogUserActivityAsync("NewsServiceStartError", $"Error starting news service: {ex.Message}");
                Console.WriteLine($"Error starting news service: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء أخبار تجريبية
        /// </summary>
        private async Task CreateSampleNewsAsync()
        {
            try
            {
                var existingNews = await _newsService.GetActiveNewsItemsAsync();
                if (existingNews.Any())
                    return; // لا تنشئ أخبار تجريبية إذا كانت موجودة

                var sampleNews = new[]
                {
                    new NewsItem
                    {
                        Title = "مرحباً بك في نظام إدارة الفواتير",
                        Content = "نظام متكامل لإدارة الفواتير والمحاسبة مع ميزات متقدمة",
                        Type = NewsType.Info,
                        Priority = NewsPriority.Normal,
                        Category = NewsCategory.System,
                        IsActive = true,
                        CreatedBy = _currentUser.Id,
                        CreatedByName = _currentUser.Username,
                        Icon = "🎉",
                        DisplayDuration = 8000
                    },
                    new NewsItem
                    {
                        Title = "تحديث النظام",
                        Content = "تم إضافة نظام الإشعارات المحسن والشريط الإخباري المتحرك",
                        Type = NewsType.Success,
                        Priority = NewsPriority.High,
                        Category = NewsCategory.System,
                        IsActive = true,
                        CreatedBy = _currentUser.Id,
                        CreatedByName = _currentUser.Username,
                        Icon = "🔄",
                        DisplayDuration = 6000
                    },
                    new NewsItem
                    {
                        Title = "نصائح الاستخدام",
                        Content = "يمكنك النقر على الشريط الإخباري للحصول على المزيد من المعلومات",
                        Type = NewsType.Info,
                        Priority = NewsPriority.Low,
                        Category = NewsCategory.System,
                        IsActive = true,
                        CreatedBy = _currentUser.Id,
                        CreatedByName = _currentUser.Username,
                        Icon = "💡",
                        DisplayDuration = 5000
                    }
                };

                foreach (var news in sampleNews)
                {
                    await _newsService.AddNewsItemAsync(news);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating sample news: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج النقر على عنصر الأخبار
        /// </summary>
        private async void NewsTicker_NewsItemClicked(object? sender, NewsItem newsItem)
        {
            try
            {
                await LogUserActivityAsync("NewsItemClicked", $"User clicked on news item: {newsItem.Title}");

                var message = $"العنوان: {newsItem.Title}\n\n" +
                             $"المحتوى: {newsItem.Content}\n\n" +
                             $"النوع: {GetNewsTypeText(newsItem.Type)}\n" +
                             $"الأولوية: {GetNewsPriorityText(newsItem.Priority)}\n" +
                             $"الفئة: {GetNewsCategoryText(newsItem.Category)}\n" +
                             $"تاريخ الإنشاء: {newsItem.CreatedDate:yyyy-MM-dd HH:mm}";

                if (!string.IsNullOrEmpty(newsItem.CreatedByName))
                    message += $"\nالمنشئ: {newsItem.CreatedByName}";

                MessageBox.Show(message, "تفاصيل الخبر", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                await LogUserActivityAsync("NewsItemClickedError", $"Error handling news item click: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج عرض عنصر الأخبار
        /// </summary>
        private async void NewsTicker_NewsItemDisplayed(object? sender, NewsItem newsItem)
        {
            try
            {
                await LogUserActivityAsync("NewsItemDisplayed", $"News item displayed: {newsItem.Title}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging news item display: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج بدء الشريط الإخباري
        /// </summary>
        private async void NewsTicker_TickerStarted(object? sender, EventArgs e)
        {
            try
            {
                await LogUserActivityAsync("NewsTickerStarted", "News ticker started");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging news ticker start: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إيقاف الشريط الإخباري
        /// </summary>
        private async void NewsTicker_TickerStopped(object? sender, EventArgs e)
        {
            try
            {
                await LogUserActivityAsync("NewsTickerStopped", "News ticker stopped");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging news ticker stop: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة إدارة الأخبار
        /// </summary>
        private async Task OpenNewsManagementAsync()
        {
            try
            {
                await LogUserActivityAsync("OpenNewsManagement", "User opened news management");

                var newsForm = new NewsManagementForm(_newsService, _currentUser);
                newsForm.ShowDialog(this);

                // تحديث الشريط الإخباري بعد إغلاق النافذة
                await _newsTicker.RefreshNewsAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الأخبار", ex.Message);
                await LogUserActivityAsync("OpenNewsManagementError", $"Error opening news management: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة خبر سريع
        /// </summary>
        private async Task AddQuickNewsAsync()
        {
            try
            {
                await LogUserActivityAsync("AddQuickNews", "User initiated quick news addition");

                var addForm = new AddEditNewsForm(_newsService, _currentUser);
                if (addForm.ShowDialog(this) == DialogResult.OK)
                {
                    await _newsTicker.RefreshNewsAsync();
                    MessageBox.Show("تم إضافة الخبر بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة الخبر", ex.Message);
                await LogUserActivityAsync("AddQuickNewsError", $"Error adding quick news: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة خبر مؤقت للشريط
        /// </summary>
        private void AddTemporaryNewsToTicker(string title, string content, NewsPriority priority = NewsPriority.Normal)
        {
            try
            {
                _newsTicker?.AddTemporaryNews(title, content, priority);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding temporary news: {ex.Message}");
            }
        }

        private string GetNewsTypeText(NewsType type)
        {
            return type switch
            {
                NewsType.General => "عام",
                NewsType.Success => "نجاح",
                NewsType.Warning => "تحذير",
                NewsType.Error => "خطأ",
                NewsType.Info => "معلومات",
                NewsType.Promotion => "ترويج",
                _ => type.ToString()
            };
        }

        private string GetNewsPriorityText(NewsPriority priority)
        {
            return priority switch
            {
                NewsPriority.Low => "منخفضة",
                NewsPriority.Normal => "عادية",
                NewsPriority.High => "عالية",
                NewsPriority.Critical => "حرجة",
                _ => priority.ToString()
            };
        }

        private string GetNewsCategoryText(NewsCategory category)
        {
            return category switch
            {
                NewsCategory.System => "النظام",
                NewsCategory.Sales => "المبيعات",
                NewsCategory.Accounting => "المحاسبة",
                NewsCategory.Inventory => "المخزون",
                NewsCategory.HR => "الموارد البشرية",
                NewsCategory.Finance => "المالية",
                NewsCategory.Marketing => "التسويق",
                NewsCategory.Technical => "تقني",
                _ => category.ToString()
            };
        }

        #endregion
    }
}