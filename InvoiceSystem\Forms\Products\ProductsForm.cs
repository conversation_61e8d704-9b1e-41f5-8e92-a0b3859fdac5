using InvoiceSystem.Models;
using InvoiceSystem.Services;
using InvoiceSystem.Forms.Products;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    public partial class ProductsForm : Form
    {
        private readonly ProductService _productService;
        private readonly ProductCategoryService _categoryService;
        private readonly UnitOfMeasureService _unitService;
        private List<Product> _products;
        private List<ProductCategory> _categories;
        private List<UnitOfMeasure> _units;
        private int _lowStockThreshold = 10;

        public ProductsForm()
        {
            InitializeComponent();
            _productService = new ProductService();
            _categoryService = new ProductCategoryService();
            _unitService = new UnitOfMeasureService();
            _products = new List<Product>();
            _categories = new List<ProductCategory>();
            _units = new List<UnitOfMeasure>();

            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadProducts();
        }

        public ProductsForm(ProductService productService)
        {
            InitializeComponent();
            _productService = productService;
            _products = new List<Product>();
            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Initialize threshold
            numLowStockThreshold.Value = _lowStockThreshold;

            LoadProducts();
        }

        private async void LoadProducts()
        {
            try
            {
                _products = await _productService.GetAllProductsAsync();
                dgvProducts.DataSource = _products;
                ConfigureDataGridView();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ConfigureDataGridView()
        {
            if (dgvProducts.Columns["Id"] != null)
                dgvProducts.Columns["Id"]!.HeaderText = "الرقم";
            if (dgvProducts.Columns["Name"] != null)
                dgvProducts.Columns["Name"]!.HeaderText = "اسم المنتج";
            if (dgvProducts.Columns["Price"] != null)
            {
                dgvProducts.Columns["Price"]!.HeaderText = "السعر";
                dgvProducts.Columns["Price"]!.DefaultCellStyle.Format = "C2";
            }
            if (dgvProducts.Columns["Stock"] != null)
                dgvProducts.Columns["Stock"]!.HeaderText = "المخزون";
            if (dgvProducts.Columns["Description"] != null)
                dgvProducts.Columns["Description"]!.HeaderText = "الوصف";
            if (dgvProducts.Columns["CreatedDate"] != null)
            {
                dgvProducts.Columns["CreatedDate"]!.HeaderText = "تاريخ الإنشاء";
                dgvProducts.Columns["CreatedDate"]!.DefaultCellStyle.Format = "dd/MM/yyyy";
            }

            dgvProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // Color rows based on stock level
            foreach (DataGridViewRow row in dgvProducts.Rows)
            {
                if (row.DataBoundItem is Product product)
                {
                    if (product.Stock <= 0)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightCoral;
                        row.DefaultCellStyle.ForeColor = Color.DarkRed;
                    }
                    else if (product.Stock <= _lowStockThreshold)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightYellow;
                        row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                    }
                }
            }
        }

        private void UpdateStatistics()
        {
            var totalProducts = _products.Count;
            var outOfStock = _products.Count(p => p.Stock <= 0);
            var lowStock = _products.Count(p => p.Stock > 0 && p.Stock <= _lowStockThreshold);
            var inStock = _products.Count(p => p.Stock > _lowStockThreshold);
            var totalValue = _products.Sum(p => p.Price * p.Stock);

            lblTotalProducts.Text = $"إجمالي المنتجات: {totalProducts}";
            lblOutOfStock.Text = $"نفد المخزون: {outOfStock}";
            lblLowStock.Text = $"مخزون منخفض: {lowStock}";
            lblInStock.Text = $"متوفر: {inStock}";
            lblTotalValue.Text = $"قيمة المخزون: {totalValue:C2}";
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditProductForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadProducts();
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                var selectedProduct = (Product)dgvProducts.SelectedRows[0].DataBoundItem;
                var editForm = new AddEditProductForm(selectedProduct);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadProducts();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                var selectedProduct = (Product)dgvProducts.SelectedRows[0].DataBoundItem;
                var result = MessageBox.Show($"هل أنت متأكد من حذف المنتج '{selectedProduct.Name}'؟",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _productService.DeleteProductAsync(selectedProduct.Id);
                        LoadProducts();
                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnUpdateStock_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                var selectedProduct = (Product)dgvProducts.SelectedRows[0].DataBoundItem;
                var stockForm = new UpdateStockForm(selectedProduct);
                if (stockForm.ShowDialog() == DialogResult.OK)
                {
                    LoadProducts();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار منتج لتحديث المخزون", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                LoadProducts();
            }
            else
            {
                try
                {
                    var searchResults = await _productService.SearchProductsAsync(txtSearch.Text);
                    dgvProducts.DataSource = searchResults;
                    ConfigureDataGridView();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnShowLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                var lowStockProducts = await _productService.GetLowStockProductsAsync(_lowStockThreshold);
                dgvProducts.DataSource = lowStockProducts;
                ConfigureDataGridView();

                if (lowStockProducts.Count == 0)
                {
                    MessageBox.Show("لا توجد منتجات بمخزون منخفض", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnShowAll_Click(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void numLowStockThreshold_ValueChanged(object sender, EventArgs e)
        {
            _lowStockThreshold = (int)numLowStockThreshold.Value;
            ConfigureDataGridView();
            UpdateStatistics();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnManageCategories_Click(object sender, EventArgs e)
        {
            ProductCategoriesForm productCategoriesForm = new ProductCategoriesForm();
            if (productCategoriesForm.ShowDialog() == DialogResult.OK)
            {
                // إعادة تحميل التصنيفات بعد إضافة تصنيف جديد
                //LoadFormDataAsync();
                //cmbCategory.SelectedIndex = -1; // إعادة تعيين الاختيار
            }
        }

        private void btnManageUnits_Click(object sender, EventArgs e)
        {
            AddEditUnitOfMeasureForm addEditUnitOfMeasureForm = new AddEditUnitOfMeasureForm();
            if (addEditUnitOfMeasureForm.ShowDialog() == DialogResult.OK)
            {
                // إعادة تحميل وحدات القياس بعد إضافة وحدة جديدة
                //LoadFormDataAsync();
                //cmbUnit.SelectedIndex = -1; // إعادة تعيين الاختيار 
            }
        }
    }
}
