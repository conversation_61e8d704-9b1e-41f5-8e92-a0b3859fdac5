using System;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using InvoiceSystem.Data;

namespace InvoiceSystem.Database
{
    /// <summary>
    /// كلاس لتحديث قاعدة البيانات وإضافة الأعمدة المفقودة
    /// </summary>
    public class DatabaseUpdater
    {
        private readonly DatabaseHelper _dbHelper;

        public DatabaseUpdater()
        {
            _dbHelper = new DatabaseHelper();
        }

        /// <summary>
        /// تحديث قاعدة البيانات لإضافة الأعمدة المفقودة
        /// </summary>
        public async Task UpdateDatabaseAsync()
        {
            try
            {
                using var connection = _dbHelper.GetConnection();
                
                // تحديث جدول المنتجات
                await UpdateProductsTableAsync(connection);
                
                // إنشاء الجداول الجديدة إذا لم تكن موجودة
                await CreateNewTablesAsync(connection);
                
                Console.WriteLine("تم تحديث قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث جدول المنتجات
        /// </summary>
        private async Task UpdateProductsTableAsync(SQLiteConnection connection)
        {
            // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
            await AddColumnIfNotExistsAsync(connection, "Products", "IsActive", "INTEGER DEFAULT 1");
            await AddColumnIfNotExistsAsync(connection, "Products", "Cost", "DECIMAL(10,2) DEFAULT 0");
            await AddColumnIfNotExistsAsync(connection, "Products", "LowStockThreshold", "INTEGER DEFAULT 10");
            await AddColumnIfNotExistsAsync(connection, "Products", "Barcode", "TEXT");
            await AddColumnIfNotExistsAsync(connection, "Products", "CategoryId", "INTEGER");
            await AddColumnIfNotExistsAsync(connection, "Products", "UnitOfMeasureId", "INTEGER");
            await AddColumnIfNotExistsAsync(connection, "Products", "UpdatedDate", "DATETIME");

            // تحديث القيم الافتراضية للمنتجات الموجودة
            var updateSql = @"
                UPDATE Products 
                SET IsActive = 1, 
                    Cost = 0, 
                    LowStockThreshold = 10,
                    UpdatedDate = datetime('now')
                WHERE IsActive IS NULL OR Cost IS NULL OR LowStockThreshold IS NULL";

            using var command = new SQLiteCommand(updateSql, connection);
            await command.ExecuteNonQueryAsync();

            // إنشاء الفهارس
            await CreateIndexIfNotExistsAsync(connection, "idx_products_isactive", "Products", "IsActive");
            await CreateIndexIfNotExistsAsync(connection, "idx_products_barcode", "Products", "Barcode");
            await CreateIndexIfNotExistsAsync(connection, "idx_products_categoryid", "Products", "CategoryId");
            await CreateIndexIfNotExistsAsync(connection, "idx_products_unitofmeasureid", "Products", "UnitOfMeasureId");
        }

        /// <summary>
        /// إنشاء الجداول الجديدة
        /// </summary>
        private async Task CreateNewTablesAsync(SQLiteConnection connection)
        {
            // إنشاء جدول تصنيفات المنتجات
            var createCategoriesTable = @"
                CREATE TABLE IF NOT EXISTS ProductCategories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Description TEXT,
                    Color TEXT DEFAULT '#007ACC',
                    IsActive INTEGER DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME
                )";

            using var command1 = new SQLiteCommand(createCategoriesTable, connection);
            await command1.ExecuteNonQueryAsync();

            // إنشاء جدول وحدات القياس
            var createUnitsTable = @"
                CREATE TABLE IF NOT EXISTS UnitsOfMeasure (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Symbol TEXT,
                    Description TEXT,
                    IsActive INTEGER DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME
                )";

            using var command2 = new SQLiteCommand(createUnitsTable, connection);
            await command2.ExecuteNonQueryAsync();

            // إنشاء جدول حركات المنتجات
            var createMovementsTable = @"
                CREATE TABLE IF NOT EXISTS ProductMovements (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    Type INTEGER NOT NULL,
                    Quantity INTEGER NOT NULL,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    Reference TEXT,
                    Notes TEXT,
                    MovementDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            using var command3 = new SQLiteCommand(createMovementsTable, connection);
            await command3.ExecuteNonQueryAsync();

            // إدراج البيانات الافتراضية
            await InsertDefaultDataAsync(connection);
        }

        /// <summary>
        /// إضافة عمود إذا لم يكن موجوداً
        /// </summary>
        private async Task AddColumnIfNotExistsAsync(SQLiteConnection connection, string tableName, string columnName, string columnDefinition)
        {
            try
            {
                // التحقق من وجود العمود
                var checkSql = $"PRAGMA table_info({tableName})";
                using var checkCommand = new SQLiteCommand(checkSql, connection);
                using var reader = await checkCommand.ExecuteReaderAsync();
                
                bool columnExists = false;
                while (await reader.ReadAsync())
                {
                    if (reader["name"].ToString() == columnName)
                    {
                        columnExists = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة العمود إذا لم يكن موجوداً
                if (!columnExists)
                {
                    var alterSql = $"ALTER TABLE {tableName} ADD COLUMN {columnName} {columnDefinition}";
                    using var alterCommand = new SQLiteCommand(alterSql, connection);
                    await alterCommand.ExecuteNonQueryAsync();
                    Console.WriteLine($"تم إضافة العمود {columnName} إلى جدول {tableName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إضافة العمود {columnName}: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء فهرس إذا لم يكن موجوداً
        /// </summary>
        private async Task CreateIndexIfNotExistsAsync(SQLiteConnection connection, string indexName, string tableName, string columnName)
        {
            try
            {
                var createIndexSql = $"CREATE INDEX IF NOT EXISTS {indexName} ON {tableName}({columnName})";
                using var command = new SQLiteCommand(createIndexSql, connection);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء الفهرس {indexName}: {ex.Message}");
            }
        }

        /// <summary>
        /// إدراج البيانات الافتراضية
        /// </summary>
        private async Task InsertDefaultDataAsync(SQLiteConnection connection)
        {
            // إدراج تصنيفات افتراضية
            var insertCategories = @"
                INSERT OR IGNORE INTO ProductCategories (Name, Description, Color) VALUES
                ('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية', '#3498db'),
                ('ملابس', 'ملابس رجالية ونسائية وأطفال', '#e74c3c'),
                ('طعام ومشروبات', 'مواد غذائية ومشروبات', '#2ecc71'),
                ('كتب وقرطاسية', 'كتب ومواد قرطاسية', '#f39c12'),
                ('أدوات منزلية', 'أدوات وأجهزة منزلية', '#9b59b6'),
                ('رياضة وترفيه', 'معدات رياضية وألعاب', '#1abc9c'),
                ('صحة وجمال', 'منتجات صحية ومستحضرات تجميل', '#e67e22'),
                ('سيارات وقطع غيار', 'قطع غيار ومعدات سيارات', '#34495e'),
                ('أثاث ومفروشات', 'أثاث منزلي ومفروشات', '#95a5a6'),
                ('أدوات وعدد', 'أدوات يدوية وعدد صناعية', '#16a085')";

            using var command1 = new SQLiteCommand(insertCategories, connection);
            await command1.ExecuteNonQueryAsync();

            // إدراج وحدات قياس افتراضية
            var insertUnits = @"
                INSERT OR IGNORE INTO UnitsOfMeasure (Name, Symbol, Description) VALUES
                ('قطعة', 'قطعة', 'وحدة العد الأساسية'),
                ('كيلوجرام', 'كجم', 'وحدة قياس الوزن'),
                ('جرام', 'جم', 'وحدة قياس الوزن الصغيرة'),
                ('لتر', 'لتر', 'وحدة قياس السوائل'),
                ('متر', 'م', 'وحدة قياس الطول'),
                ('سنتيمتر', 'سم', 'وحدة قياس الطول الصغيرة'),
                ('صندوق', 'صندوق', 'وحدة التعبئة'),
                ('كرتونة', 'كرتونة', 'وحدة التعبئة الكبيرة'),
                ('زجاجة', 'زجاجة', 'وحدة تعبئة السوائل'),
                ('علبة', 'علبة', 'وحدة التعبئة الصغيرة')";

            using var command2 = new SQLiteCommand(insertUnits, connection);
            await command2.ExecuteNonQueryAsync();

            Console.WriteLine("تم إدراج البيانات الافتراضية بنجاح");
        }
    }
}
