using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    /// <summary>
    /// نافذة إدارة وحدات القياس
    /// </summary>
    public partial class UnitsOfMeasureForm : Form
    {
        private readonly UnitOfMeasureService _unitService;
        private List<UnitOfMeasure> _units;

        public UnitsOfMeasureForm()
        {
            InitializeComponent();
            _unitService = new UnitOfMeasureService();
            _units = new List<UnitOfMeasure>();
            
            this.Text = "إدارة وحدات القياس";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            SetupDataGridView();
            LoadUnitsAsync();
        }

        private void SetupDataGridView()
        {
            dgvUnits.AutoGenerateColumns = false;
            dgvUnits.AllowUserToAddRows = false;
            dgvUnits.AllowUserToDeleteRows = false;
            dgvUnits.ReadOnly = true;
            dgvUnits.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUnits.MultiSelect = false;

            // إعداد الأعمدة
            dgvUnits.Columns.Clear();

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                HeaderText = "المعرف",
                Width = 60,
                Visible = false
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                DataPropertyName = "Name",
                HeaderText = "اسم الوحدة",
                Width = 200,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Symbol",
                DataPropertyName = "Symbol",
                HeaderText = "الرمز",
                Width = 80
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                DataPropertyName = "Description",
                HeaderText = "الوصف",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvUnits.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Width = 60
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                DataPropertyName = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
            });

            // تلوين الصفوف حسب الحالة
            dgvUnits.CellFormatting += DgvUnits_CellFormatting;
        }

        private void DgvUnits_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var unit = dgvUnits.Rows[e.RowIndex].DataBoundItem as UnitOfMeasure;
                if (unit != null && !unit.IsActive)
                {
                    e.CellStyle.ForeColor = Color.Gray;
                    e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Italic);
                }
            }
        }

        private async void LoadUnitsAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل وحدات القياس...";
                lblStatus.ForeColor = Color.Blue;

                _units = await _unitService.GetAllUnitsAsync();
                dgvUnits.DataSource = _units;

                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_units.Count} وحدة قياس";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في تحميل وحدات القياس";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"خطأ في تحميل وحدات القياس: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _unitService.GetUnitStatisticsAsync();
                lblTotalUnits.Text = $"إجمالي الوحدات: {stats["TotalUnits"]}";
                lblActiveUnits.Text = $"وحدات نشطة: {stats["ActiveUnits"]}";
                lblInactiveUnits.Text = $"وحدات غير نشطة: {stats["InactiveUnits"]}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditUnitOfMeasureForm();
            if (addForm.ShowDialog(this) == DialogResult.OK)
            {
                LoadUnitsAsync();
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار وحدة قياس للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUnit = dgvUnits.SelectedRows[0].DataBoundItem as UnitOfMeasure;
            if (selectedUnit != null)
            {
                var editForm = new AddEditUnitOfMeasureForm(selectedUnit);
                if (editForm.ShowDialog(this) == DialogResult.OK)
                {
                    LoadUnitsAsync();
                }
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار وحدة قياس للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUnit = dgvUnits.SelectedRows[0].DataBoundItem as UnitOfMeasure;
            if (selectedUnit != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف وحدة القياس '{selectedUnit.Name}'؟\n\nملاحظة: لا يمكن حذف الوحدة إذا كانت مستخدمة في منتجات.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _unitService.DeleteUnitAsync(selectedUnit.Id);
                        MessageBox.Show("تم حذف وحدة القياس بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadUnitsAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف وحدة القياس: {ex.Message}", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void btnToggleStatus_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار وحدة قياس", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUnit = dgvUnits.SelectedRows[0].DataBoundItem as UnitOfMeasure;
            if (selectedUnit != null)
            {
                try
                {
                    await _unitService.ToggleUnitStatusAsync(selectedUnit.Id);
                    var status = selectedUnit.IsActive ? "إلغاء تفعيل" : "تفعيل";
                    MessageBox.Show($"تم {status} وحدة القياس بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadUnitsAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تغيير حالة وحدة القياس: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvUnits.DataSource = _units;
            }
            else
            {
                try
                {
                    var searchResults = await _unitService.SearchUnitsAsync(txtSearch.Text);
                    dgvUnits.DataSource = searchResults;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error searching units: {ex.Message}");
                }
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadUnitsAsync();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dgvUnits_DoubleClick(object sender, EventArgs e)
        {
            btnEdit_Click(sender, e);
        }
    }
}
