using Dapper;
using InvoiceSystem.Data;
using InvoiceSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InvoiceSystem.Services
{
    /// <summary>
    /// خدمة إدارة وحدات القياس
    /// </summary>
    public class UnitOfMeasureService
    {
        private readonly DatabaseHelper _dbHelper;

        public UnitOfMeasureService()
        {
            _dbHelper = new DatabaseHelper();
        }

        public UnitOfMeasureService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        /// <summary>
        /// الحصول على جميع وحدات القياس
        /// </summary>
        public async Task<List<UnitOfMeasure>> GetAllUnitsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var units = await connection.QueryAsync<UnitOfMeasure>(
                "SELECT * FROM UnitsOfMeasure WHERE IsActive = 1 ORDER BY Name");
            return units.ToList();
        }

        /// <summary>
        /// الحصول على وحدة قياس بالمعرف
        /// </summary>
        public async Task<UnitOfMeasure?> GetUnitByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            return await connection.QueryFirstOrDefaultAsync<UnitOfMeasure>(
                "SELECT * FROM UnitsOfMeasure WHERE Id = @Id", new { Id = id });
        }

        /// <summary>
        /// إضافة وحدة قياس جديدة
        /// </summary>
        public async Task<int> AddUnitAsync(UnitOfMeasure unit)
        {
            using var connection = _dbHelper.GetConnection();
            unit.CreatedDate = DateTime.Now;

            var sql = @"INSERT INTO UnitsOfMeasure (Name, Symbol, Description, IsActive, CreatedDate)
                       VALUES (@Name, @Symbol, @Description, @IsActive, @CreatedDate);
                       SELECT last_insert_rowid();";

            return await connection.QuerySingleAsync<int>(sql, unit);
        }

        /// <summary>
        /// تحديث وحدة قياس
        /// </summary>
        public async Task<bool> UpdateUnitAsync(UnitOfMeasure unit)
        {
            using var connection = _dbHelper.GetConnection();

            var sql = @"UPDATE UnitsOfMeasure
                       SET Name = @Name, Symbol = @Symbol, Description = @Description, IsActive = @IsActive
                       WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, unit);
            return rowsAffected > 0;
        }

        /// <summary>
        /// حذف وحدة قياس
        /// </summary>
        public async Task<bool> DeleteUnitAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            
            // التحقق من وجود منتجات مرتبطة
            var productCount = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE UnitOfMeasureId = @Id", new { Id = id });
            
            if (productCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف وحدة القياس لوجود منتجات مرتبطة بها");
            }

            var rowsAffected = await connection.ExecuteAsync(
                "DELETE FROM UnitsOfMeasure WHERE Id = @Id", new { Id = id });
            return rowsAffected > 0;
        }

        /// <summary>
        /// البحث في وحدات القياس
        /// </summary>
        public async Task<List<UnitOfMeasure>> SearchUnitsAsync(string searchTerm)
        {
            using var connection = _dbHelper.GetConnection();
            var units = await connection.QueryAsync<UnitOfMeasure>(
                @"SELECT * FROM UnitsOfMeasure 
                  WHERE IsActive = 1 AND (Name LIKE @SearchTerm OR Symbol LIKE @SearchTerm OR Description LIKE @SearchTerm)
                  ORDER BY Name",
                new { SearchTerm = $"%{searchTerm}%" });
            return units.ToList();
        }

        /// <summary>
        /// الحصول على إحصائيات وحدات القياس
        /// </summary>
        public async Task<Dictionary<string, object>> GetUnitStatisticsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            
            var totalUnits = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM UnitsOfMeasure WHERE IsActive = 1");
            
            var unitsWithProducts = await connection.QuerySingleAsync<int>(
                @"SELECT COUNT(DISTINCT UnitOfMeasureId) FROM Products 
                  WHERE UnitOfMeasureId IS NOT NULL AND IsActive = 1");
            
            var unitsWithoutProducts = totalUnits - unitsWithProducts;

            return new Dictionary<string, object>
            {
                { "TotalUnits", totalUnits },
                { "UnitsWithProducts", unitsWithProducts },
                { "UnitsWithoutProducts", unitsWithoutProducts }
            };
        }

        /// <summary>
        /// الحصول على وحدات القياس مع عدد المنتجات
        /// </summary>
        public async Task<List<dynamic>> GetUnitsWithProductCountAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var result = await connection.QueryAsync(
                @"SELECT u.*, 
                         COALESCE(p.ProductCount, 0) as ProductCount
                  FROM UnitsOfMeasure u
                  LEFT JOIN (
                      SELECT UnitOfMeasureId, COUNT(*) as ProductCount
                      FROM Products 
                      WHERE IsActive = 1
                      GROUP BY UnitOfMeasureId
                  ) p ON u.Id = p.UnitOfMeasureId
                  WHERE u.IsActive = 1
                  ORDER BY u.Name");
            return result.ToList();
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل وحدة قياس
        /// </summary>
        public async Task<bool> ToggleUnitStatusAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            
            var currentStatus = await connection.QuerySingleAsync<bool>(
                "SELECT IsActive FROM UnitsOfMeasure WHERE Id = @Id", new { Id = id });
            
            var newStatus = !currentStatus;
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE UnitsOfMeasure SET IsActive = @IsActive WHERE Id = @Id",
                new { IsActive = newStatus, Id = id });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// الحصول على وحدات القياس النشطة فقط
        /// </summary>
        public async Task<List<UnitOfMeasure>> GetActiveUnitsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var units = await connection.QueryAsync<UnitOfMeasure>(
                "SELECT * FROM UnitsOfMeasure WHERE IsActive = 1 ORDER BY Name");
            return units.ToList();
        }

        /// <summary>
        /// التحقق من وجود وحدة قياس بنفس الاسم
        /// </summary>
        public async Task<bool> IsUnitNameExistsAsync(string name, int? excludeId = null)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM UnitsOfMeasure WHERE Name = @Name";
            var parameters = new { Name = name };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                parameters = new { Name = name, ExcludeId = excludeId.Value };
            }

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count > 0;
        }

        /// <summary>
        /// التحقق من وجود وحدة قياس بنفس الرمز
        /// </summary>
        public async Task<bool> IsUnitSymbolExistsAsync(string symbol, int? excludeId = null)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM UnitsOfMeasure WHERE Symbol = @Symbol";
            var parameters = new { Symbol = symbol };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                parameters = new { Symbol = symbol, ExcludeId = excludeId.Value };
            }

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count > 0;
        }
    }
}
