namespace InvoiceSystem.Forms.Products
{
    partial class AddEditProductForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private System.Windows.Forms.GroupBox groupBoxBasicInfo;
        private System.Windows.Forms.GroupBox groupBoxPricing;
        private System.Windows.Forms.GroupBox groupBoxInventory;
        private System.Windows.Forms.GroupBox groupBoxClassification;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblBarcode;
        private System.Windows.Forms.TextBox txtBarcode;
        private System.Windows.Forms.Button btnGenerateBarcode;
        private System.Windows.Forms.Label lblPrice;
        private System.Windows.Forms.NumericUpDown numPrice;
        private System.Windows.Forms.Label lblCost;
        private System.Windows.Forms.NumericUpDown numCost;
        private System.Windows.Forms.Label lblProfitMargin;
        private System.Windows.Forms.Label lblStock;
        private System.Windows.Forms.NumericUpDown numStock;
        private System.Windows.Forms.Label lblLowStockThreshold;
        private System.Windows.Forms.NumericUpDown numLowStockThreshold;
        private System.Windows.Forms.Label lblStockStatus;
        private System.Windows.Forms.Label lblCategory;
        private System.Windows.Forms.ComboBox cmbCategory;
        private System.Windows.Forms.Button btnAddCategory;
        private System.Windows.Forms.Label lblUnit;
        private System.Windows.Forms.ComboBox cmbUnit;
        private System.Windows.Forms.Button btnAddUnit;
        private System.Windows.Forms.CheckBox chkIsActive;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;

        private void InitializeComponent()
        {
            lblName = new Label();
            txtName = new TextBox();
            lblDescription = new Label();
            txtDescription = new TextBox();
            lblPrice = new Label();
            numPrice = new NumericUpDown();
            lblCost = new Label();
            numCost = new NumericUpDown();
            lblStock = new Label();
            numStock = new NumericUpDown();
            lblLowStockThreshold = new Label();
            numLowStockThreshold = new NumericUpDown();
            lblBarcode = new Label();
            txtBarcode = new TextBox();
            lblCategory = new Label();
            cmbCategory = new ComboBox();
            lblUnit = new Label();
            cmbUnit = new ComboBox();
            chkIsActive = new CheckBox();
            btnSave = new Button();
            btnCancel = new Button();
            lblProfitMargin = new Label();
            lblStockStatus = new Label();
            btnGenerateBarcode = new Button();
            btnAddCategory = new Button();
            btnAddUnit = new Button();
            groupBoxBasicInfo = new GroupBox();
            groupBoxPricing = new GroupBox();
            groupBoxInventory = new GroupBox();
            groupBoxClassification = new GroupBox();
            ((System.ComponentModel.ISupportInitialize)numPrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numCost).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numStock).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numLowStockThreshold).BeginInit();
            groupBoxBasicInfo.SuspendLayout();
            groupBoxPricing.SuspendLayout();
            groupBoxInventory.SuspendLayout();
            groupBoxClassification.SuspendLayout();
            SuspendLayout();
            // 
            // lblName
            // 
            lblName.AutoSize = true;
            lblName.Location = new Point(15, 25);
            lblName.Name = "lblName";
            lblName.Size = new Size(62, 15);
            lblName.TabIndex = 0;
            lblName.Text = "اسم المنتج:";
            // 
            // txtName
            // 
            txtName.Location = new Point(15, 45);
            txtName.MaxLength = 200;
            txtName.Name = "txtName";
            txtName.Size = new Size(370, 23);
            txtName.TabIndex = 1;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Location = new Point(15, 80);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(47, 15);
            lblDescription.TabIndex = 2;
            lblDescription.Text = "الوصف:";
            // 
            // txtDescription
            // 
            txtDescription.Location = new Point(15, 100);
            txtDescription.MaxLength = 500;
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.ScrollBars = ScrollBars.Vertical;
            txtDescription.Size = new Size(370, 45);
            txtDescription.TabIndex = 3;
            // 
            // lblPrice
            // 
            lblPrice.AutoSize = true;
            lblPrice.Location = new Point(15, 25);
            lblPrice.Name = "lblPrice";
            lblPrice.Size = new Size(39, 15);
            lblPrice.TabIndex = 0;
            lblPrice.Text = "السعر:";
            // 
            // numPrice
            // 
            numPrice.DecimalPlaces = 2;
            numPrice.Location = new Point(15, 45);
            numPrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numPrice.Name = "numPrice";
            numPrice.Size = new Size(100, 23);
            numPrice.TabIndex = 1;
            numPrice.ValueChanged += numPrice_ValueChanged;
            // 
            // lblCost
            // 
            lblCost.AutoSize = true;
            lblCost.Location = new Point(130, 25);
            lblCost.Name = "lblCost";
            lblCost.Size = new Size(43, 15);
            lblCost.TabIndex = 2;
            lblCost.Text = "التكلفة:";
            // 
            // numCost
            // 
            numCost.DecimalPlaces = 2;
            numCost.Location = new Point(130, 45);
            numCost.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numCost.Name = "numCost";
            numCost.Size = new Size(100, 23);
            numCost.TabIndex = 3;
            numCost.ValueChanged += numCost_ValueChanged;
            // 
            // lblStock
            // 
            lblStock.AutoSize = true;
            lblStock.Location = new Point(15, 25);
            lblStock.Name = "lblStock";
            lblStock.Size = new Size(40, 15);
            lblStock.TabIndex = 0;
            lblStock.Text = "الكمية:";
            // 
            // numStock
            // 
            numStock.Location = new Point(15, 45);
            numStock.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numStock.Name = "numStock";
            numStock.Size = new Size(100, 23);
            numStock.TabIndex = 1;
            numStock.ValueChanged += numStock_ValueChanged;
            // 
            // lblLowStockThreshold
            // 
            lblLowStockThreshold.AutoSize = true;
            lblLowStockThreshold.Location = new Point(130, 25);
            lblLowStockThreshold.Name = "lblLowStockThreshold";
            lblLowStockThreshold.Size = new Size(115, 15);
            lblLowStockThreshold.TabIndex = 2;
            lblLowStockThreshold.Text = "حد المخزون المنخفض:";
            // 
            // numLowStockThreshold
            // 
            numLowStockThreshold.Location = new Point(130, 45);
            numLowStockThreshold.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
            numLowStockThreshold.Name = "numLowStockThreshold";
            numLowStockThreshold.Size = new Size(100, 23);
            numLowStockThreshold.TabIndex = 3;
            numLowStockThreshold.Value = new decimal(new int[] { 10, 0, 0, 0 });
            numLowStockThreshold.ValueChanged += numLowStockThreshold_ValueChanged;
            // 
            // lblBarcode
            // 
            lblBarcode.AutoSize = true;
            lblBarcode.Location = new Point(15, 155);
            lblBarcode.Name = "lblBarcode";
            lblBarcode.Size = new Size(46, 15);
            lblBarcode.TabIndex = 4;
            lblBarcode.Text = "الباركود:";
            // 
            // txtBarcode
            // 
            txtBarcode.Location = new Point(80, 152);
            txtBarcode.MaxLength = 100;
            txtBarcode.Name = "txtBarcode";
            txtBarcode.Size = new Size(200, 23);
            txtBarcode.TabIndex = 5;
            // 
            // lblCategory
            // 
            lblCategory.AutoSize = true;
            lblCategory.Location = new Point(15, 25);
            lblCategory.Name = "lblCategory";
            lblCategory.Size = new Size(53, 15);
            lblCategory.TabIndex = 0;
            lblCategory.Text = "التصنيف:";
            // 
            // cmbCategory
            // 
            cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCategory.FormattingEnabled = true;
            cmbCategory.Location = new Point(15, 45);
            cmbCategory.Name = "cmbCategory";
            cmbCategory.Size = new Size(150, 23);
            cmbCategory.TabIndex = 1;
            // 
            // lblUnit
            // 
            lblUnit.AutoSize = true;
            lblUnit.Location = new Point(220, 25);
            lblUnit.Name = "lblUnit";
            lblUnit.Size = new Size(72, 15);
            lblUnit.TabIndex = 3;
            lblUnit.Text = "وحدة القياس:";
            // 
            // cmbUnit
            // 
            cmbUnit.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbUnit.FormattingEnabled = true;
            cmbUnit.Location = new Point(220, 45);
            cmbUnit.Name = "cmbUnit";
            cmbUnit.Size = new Size(120, 23);
            cmbUnit.TabIndex = 4;
            // 
            // chkIsActive
            // 
            chkIsActive.AutoSize = true;
            chkIsActive.Checked = true;
            chkIsActive.CheckState = CheckState.Checked;
            chkIsActive.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            chkIsActive.Location = new Point(15, 85);
            chkIsActive.Name = "chkIsActive";
            chkIsActive.Size = new Size(50, 19);
            chkIsActive.TabIndex = 6;
            chkIsActive.Text = "نشط";
            chkIsActive.UseVisualStyleBackColor = true;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Location = new Point(430, 280);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(120, 40);
            btnSave.TabIndex = 4;
            btnSave.Text = "💾 حفظ";
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnCancel
            // 
            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(560, 280);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(120, 40);
            btnCancel.TabIndex = 5;
            btnCancel.Text = "❌ إلغاء";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // lblProfitMargin
            // 
            lblProfitMargin.AutoSize = true;
            lblProfitMargin.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblProfitMargin.ForeColor = Color.Green;
            lblProfitMargin.Location = new Point(15, 80);
            lblProfitMargin.Name = "lblProfitMargin";
            lblProfitMargin.Size = new Size(81, 15);
            lblProfitMargin.TabIndex = 4;
            lblProfitMargin.Text = "هامش الربح: --";
            // 
            // lblStockStatus
            // 
            lblStockStatus.AutoSize = true;
            lblStockStatus.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblStockStatus.ForeColor = Color.Green;
            lblStockStatus.Location = new Point(15, 80);
            lblStockStatus.Name = "lblStockStatus";
            lblStockStatus.Size = new Size(103, 15);
            lblStockStatus.TabIndex = 4;
            lblStockStatus.Text = "حالة المخزون: متوفر";
            // 
            // btnGenerateBarcode
            // 
            btnGenerateBarcode.BackColor = Color.FromArgb(52, 152, 219);
            btnGenerateBarcode.ForeColor = Color.White;
            btnGenerateBarcode.Location = new Point(290, 150);
            btnGenerateBarcode.Name = "btnGenerateBarcode";
            btnGenerateBarcode.Size = new Size(95, 27);
            btnGenerateBarcode.TabIndex = 6;
            btnGenerateBarcode.Text = "🔢 توليد تلقائي";
            btnGenerateBarcode.UseVisualStyleBackColor = false;
            // 
            // btnAddCategory
            // 
            btnAddCategory.BackColor = Color.FromArgb(46, 204, 113);
            btnAddCategory.ForeColor = Color.White;
            btnAddCategory.Location = new Point(175, 43);
            btnAddCategory.Name = "btnAddCategory";
            btnAddCategory.Size = new Size(30, 27);
            btnAddCategory.TabIndex = 2;
            btnAddCategory.Text = "➕";
            btnAddCategory.UseVisualStyleBackColor = false;
            btnAddCategory.Click += btnAddCategory_Click;
            // 
            // btnAddUnit
            // 
            btnAddUnit.BackColor = Color.FromArgb(46, 204, 113);
            btnAddUnit.ForeColor = Color.White;
            btnAddUnit.Location = new Point(350, 43);
            btnAddUnit.Name = "btnAddUnit";
            btnAddUnit.Size = new Size(30, 27);
            btnAddUnit.TabIndex = 5;
            btnAddUnit.Text = "➕";
            btnAddUnit.UseVisualStyleBackColor = false;
            btnAddUnit.Click += btnAddUnit_Click;
            // 
            // groupBoxBasicInfo
            // 
            groupBoxBasicInfo.Controls.Add(lblName);
            groupBoxBasicInfo.Controls.Add(txtName);
            groupBoxBasicInfo.Controls.Add(lblDescription);
            groupBoxBasicInfo.Controls.Add(txtDescription);
            groupBoxBasicInfo.Controls.Add(lblBarcode);
            groupBoxBasicInfo.Controls.Add(txtBarcode);
            groupBoxBasicInfo.Controls.Add(btnGenerateBarcode);
            groupBoxBasicInfo.Location = new Point(12, 12);
            groupBoxBasicInfo.Name = "groupBoxBasicInfo";
            groupBoxBasicInfo.Size = new Size(400, 180);
            groupBoxBasicInfo.TabIndex = 0;
            groupBoxBasicInfo.TabStop = false;
            groupBoxBasicInfo.Text = "المعلومات الأساسية";
            // 
            // groupBoxPricing
            // 
            groupBoxPricing.Controls.Add(lblPrice);
            groupBoxPricing.Controls.Add(numPrice);
            groupBoxPricing.Controls.Add(lblCost);
            groupBoxPricing.Controls.Add(numCost);
            groupBoxPricing.Controls.Add(lblProfitMargin);
            groupBoxPricing.Location = new Point(430, 12);
            groupBoxPricing.Name = "groupBoxPricing";
            groupBoxPricing.Size = new Size(250, 120);
            groupBoxPricing.TabIndex = 1;
            groupBoxPricing.TabStop = false;
            groupBoxPricing.Text = "التسعير";
            // 
            // groupBoxInventory
            // 
            groupBoxInventory.Controls.Add(lblStock);
            groupBoxInventory.Controls.Add(numStock);
            groupBoxInventory.Controls.Add(lblLowStockThreshold);
            groupBoxInventory.Controls.Add(numLowStockThreshold);
            groupBoxInventory.Controls.Add(lblStockStatus);
            groupBoxInventory.Location = new Point(430, 145);
            groupBoxInventory.Name = "groupBoxInventory";
            groupBoxInventory.Size = new Size(250, 120);
            groupBoxInventory.TabIndex = 2;
            groupBoxInventory.TabStop = false;
            groupBoxInventory.Text = "المخزون";
            // 
            // groupBoxClassification
            // 
            groupBoxClassification.Controls.Add(lblCategory);
            groupBoxClassification.Controls.Add(cmbCategory);
            groupBoxClassification.Controls.Add(btnAddCategory);
            groupBoxClassification.Controls.Add(lblUnit);
            groupBoxClassification.Controls.Add(cmbUnit);
            groupBoxClassification.Controls.Add(btnAddUnit);
            groupBoxClassification.Controls.Add(chkIsActive);
            groupBoxClassification.Location = new Point(12, 200);
            groupBoxClassification.Name = "groupBoxClassification";
            groupBoxClassification.Size = new Size(400, 120);
            groupBoxClassification.TabIndex = 3;
            groupBoxClassification.TabStop = false;
            groupBoxClassification.Text = "التصنيف والوحدة";
            // 
            // AddEditProductForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(700, 340);
            Controls.Add(groupBoxBasicInfo);
            Controls.Add(groupBoxPricing);
            Controls.Add(groupBoxInventory);
            Controls.Add(groupBoxClassification);
            Controls.Add(btnSave);
            Controls.Add(btnCancel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddEditProductForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "إضافة/تعديل منتج";
            ((System.ComponentModel.ISupportInitialize)numPrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numCost).EndInit();
            ((System.ComponentModel.ISupportInitialize)numStock).EndInit();
            ((System.ComponentModel.ISupportInitialize)numLowStockThreshold).EndInit();
            groupBoxBasicInfo.ResumeLayout(false);
            groupBoxBasicInfo.PerformLayout();
            groupBoxPricing.ResumeLayout(false);
            groupBoxPricing.PerformLayout();
            groupBoxInventory.ResumeLayout(false);
            groupBoxInventory.PerformLayout();
            groupBoxClassification.ResumeLayout(false);
            groupBoxClassification.PerformLayout();
            ResumeLayout(false);
        }

        #endregion
    }
}
