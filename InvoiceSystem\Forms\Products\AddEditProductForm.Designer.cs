namespace InvoiceSystem.Forms.Products
{
    partial class AddEditProductForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private System.Windows.Forms.GroupBox groupBoxBasicInfo;
        private System.Windows.Forms.GroupBox groupBoxPricing;
        private System.Windows.Forms.GroupBox groupBoxInventory;
        private System.Windows.Forms.GroupBox groupBoxClassification;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblBarcode;
        private System.Windows.Forms.TextBox txtBarcode;
        private System.Windows.Forms.Button btnGenerateBarcode;
        private System.Windows.Forms.Label lblPrice;
        private System.Windows.Forms.NumericUpDown numPrice;
        private System.Windows.Forms.Label lblCost;
        private System.Windows.Forms.NumericUpDown numCost;
        private System.Windows.Forms.Label lblProfitMargin;
        private System.Windows.Forms.Label lblStock;
        private System.Windows.Forms.NumericUpDown numStock;
        private System.Windows.Forms.Label lblLowStockThreshold;
        private System.Windows.Forms.NumericUpDown numLowStockThreshold;
        private System.Windows.Forms.Label lblStockStatus;
        private System.Windows.Forms.Label lblCategory;
        private System.Windows.Forms.ComboBox cmbCategory;
        private System.Windows.Forms.Button btnAddCategory;
        private System.Windows.Forms.Label lblUnit;
        private System.Windows.Forms.ComboBox cmbUnit;
        private System.Windows.Forms.Button btnAddUnit;
        private System.Windows.Forms.CheckBox chkIsActive;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;

        private void InitializeComponent()
        {
            this.lblName = new System.Windows.Forms.Label();
            this.txtName = new System.Windows.Forms.TextBox();
            this.lblDescription = new System.Windows.Forms.Label();
            this.txtDescription = new System.Windows.Forms.TextBox();
            this.lblPrice = new System.Windows.Forms.Label();
            this.numPrice = new System.Windows.Forms.NumericUpDown();
            this.lblCost = new System.Windows.Forms.Label();
            this.numCost = new System.Windows.Forms.NumericUpDown();
            this.lblStock = new System.Windows.Forms.Label();
            this.numStock = new System.Windows.Forms.NumericUpDown();
            this.lblLowStockThreshold = new System.Windows.Forms.Label();
            this.numLowStockThreshold = new System.Windows.Forms.NumericUpDown();
            this.lblBarcode = new System.Windows.Forms.Label();
            this.txtBarcode = new System.Windows.Forms.TextBox();
            this.lblCategory = new System.Windows.Forms.Label();
            this.cmbCategory = new System.Windows.Forms.ComboBox();
            this.lblUnit = new System.Windows.Forms.Label();
            this.cmbUnit = new System.Windows.Forms.ComboBox();
            this.chkIsActive = new System.Windows.Forms.CheckBox();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblProfitMargin = new System.Windows.Forms.Label();
            this.lblStockStatus = new System.Windows.Forms.Label();
            this.btnGenerateBarcode = new System.Windows.Forms.Button();
            this.btnAddCategory = new System.Windows.Forms.Button();
            this.btnAddUnit = new System.Windows.Forms.Button();
            this.groupBoxBasicInfo = new System.Windows.Forms.GroupBox();
            this.groupBoxPricing = new System.Windows.Forms.GroupBox();
            this.groupBoxInventory = new System.Windows.Forms.GroupBox();
            this.groupBoxClassification = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCost)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numStock)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLowStockThreshold)).BeginInit();
            this.groupBoxBasicInfo.SuspendLayout();
            this.groupBoxPricing.SuspendLayout();
            this.groupBoxInventory.SuspendLayout();
            this.groupBoxClassification.SuspendLayout();
            this.SuspendLayout();

            //
            // groupBoxBasicInfo
            //
            this.groupBoxBasicInfo.Controls.Add(this.lblName);
            this.groupBoxBasicInfo.Controls.Add(this.txtName);
            this.groupBoxBasicInfo.Controls.Add(this.lblDescription);
            this.groupBoxBasicInfo.Controls.Add(this.txtDescription);
            this.groupBoxBasicInfo.Controls.Add(this.lblBarcode);
            this.groupBoxBasicInfo.Controls.Add(this.txtBarcode);
            this.groupBoxBasicInfo.Controls.Add(this.btnGenerateBarcode);
            this.groupBoxBasicInfo.Location = new System.Drawing.Point(12, 12);
            this.groupBoxBasicInfo.Name = "groupBoxBasicInfo";
            this.groupBoxBasicInfo.Size = new System.Drawing.Size(400, 180);
            this.groupBoxBasicInfo.TabIndex = 0;
            this.groupBoxBasicInfo.TabStop = false;
            this.groupBoxBasicInfo.Text = "المعلومات الأساسية";

            //
            // lblName
            //
            this.lblName.AutoSize = true;
            this.lblName.Location = new System.Drawing.Point(15, 25);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(70, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "اسم المنتج:";

            //
            // txtName
            //
            this.txtName.Location = new System.Drawing.Point(15, 45);
            this.txtName.MaxLength = 200;
            this.txtName.Name = "txtName";
            this.txtName.Size = new System.Drawing.Size(370, 23);
            this.txtName.TabIndex = 1;

            //
            // lblDescription
            //
            this.lblDescription.AutoSize = true;
            this.lblDescription.Location = new System.Drawing.Point(15, 80);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(38, 15);
            this.lblDescription.TabIndex = 2;
            this.lblDescription.Text = "الوصف:";

            //
            // txtDescription
            //
            this.txtDescription.Location = new System.Drawing.Point(15, 100);
            this.txtDescription.MaxLength = 500;
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtDescription.Size = new System.Drawing.Size(370, 45);
            this.txtDescription.TabIndex = 3;

            //
            // lblBarcode
            //
            this.lblBarcode.AutoSize = true;
            this.lblBarcode.Location = new System.Drawing.Point(15, 155);
            this.lblBarcode.Name = "lblBarcode";
            this.lblBarcode.Size = new System.Drawing.Size(50, 15);
            this.lblBarcode.TabIndex = 4;
            this.lblBarcode.Text = "الباركود:";

            //
            // txtBarcode
            //
            this.txtBarcode.Location = new System.Drawing.Point(80, 152);
            this.txtBarcode.MaxLength = 100;
            this.txtBarcode.Name = "txtBarcode";
            this.txtBarcode.Size = new System.Drawing.Size(200, 23);
            this.txtBarcode.TabIndex = 5;

            //
            // btnGenerateBarcode
            //
            this.btnGenerateBarcode.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnGenerateBarcode.ForeColor = System.Drawing.Color.White;
            this.btnGenerateBarcode.Location = new System.Drawing.Point(290, 150);
            this.btnGenerateBarcode.Name = "btnGenerateBarcode";
            this.btnGenerateBarcode.Size = new System.Drawing.Size(95, 27);
            this.btnGenerateBarcode.TabIndex = 6;
            this.btnGenerateBarcode.Text = "🔢 توليد تلقائي";
            this.btnGenerateBarcode.UseVisualStyleBackColor = false;

            //
            // groupBoxPricing
            //
            this.groupBoxPricing.Controls.Add(this.lblPrice);
            this.groupBoxPricing.Controls.Add(this.numPrice);
            this.groupBoxPricing.Controls.Add(this.lblCost);
            this.groupBoxPricing.Controls.Add(this.numCost);
            this.groupBoxPricing.Controls.Add(this.lblProfitMargin);
            this.groupBoxPricing.Location = new System.Drawing.Point(430, 12);
            this.groupBoxPricing.Name = "groupBoxPricing";
            this.groupBoxPricing.Size = new System.Drawing.Size(250, 120);
            this.groupBoxPricing.TabIndex = 1;
            this.groupBoxPricing.TabStop = false;
            this.groupBoxPricing.Text = "التسعير";

            //
            // lblPrice
            //
            this.lblPrice.AutoSize = true;
            this.lblPrice.Location = new System.Drawing.Point(15, 25);
            this.lblPrice.Name = "lblPrice";
            this.lblPrice.Size = new System.Drawing.Size(35, 15);
            this.lblPrice.TabIndex = 0;
            this.lblPrice.Text = "السعر:";

            //
            // numPrice
            //
            this.numPrice.DecimalPlaces = 2;
            this.numPrice.Location = new System.Drawing.Point(15, 45);
            this.numPrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.numPrice.Name = "numPrice";
            this.numPrice.Size = new System.Drawing.Size(100, 23);
            this.numPrice.TabIndex = 1;
            this.numPrice.ValueChanged += new System.EventHandler(this.numPrice_ValueChanged);

            //
            // lblCost
            //
            this.lblCost.AutoSize = true;
            this.lblCost.Location = new System.Drawing.Point(130, 25);
            this.lblCost.Name = "lblCost";
            this.lblCost.Size = new System.Drawing.Size(40, 15);
            this.lblCost.TabIndex = 2;
            this.lblCost.Text = "التكلفة:";

            //
            // numCost
            //
            this.numCost.DecimalPlaces = 2;
            this.numCost.Location = new System.Drawing.Point(130, 45);
            this.numCost.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.numCost.Name = "numCost";
            this.numCost.Size = new System.Drawing.Size(100, 23);
            this.numCost.TabIndex = 3;
            this.numCost.ValueChanged += new System.EventHandler(this.numCost_ValueChanged);

            //
            // lblProfitMargin
            //
            this.lblProfitMargin.AutoSize = true;
            this.lblProfitMargin.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblProfitMargin.ForeColor = System.Drawing.Color.Green;
            this.lblProfitMargin.Location = new System.Drawing.Point(15, 80);
            this.lblProfitMargin.Name = "lblProfitMargin";
            this.lblProfitMargin.Size = new System.Drawing.Size(70, 15);
            this.lblProfitMargin.TabIndex = 4;
            this.lblProfitMargin.Text = "هامش الربح: --";

            //
            // groupBoxInventory
            //
            this.groupBoxInventory.Controls.Add(this.lblStock);
            this.groupBoxInventory.Controls.Add(this.numStock);
            this.groupBoxInventory.Controls.Add(this.lblLowStockThreshold);
            this.groupBoxInventory.Controls.Add(this.numLowStockThreshold);
            this.groupBoxInventory.Controls.Add(this.lblStockStatus);
            this.groupBoxInventory.Location = new System.Drawing.Point(430, 145);
            this.groupBoxInventory.Name = "groupBoxInventory";
            this.groupBoxInventory.Size = new System.Drawing.Size(250, 120);
            this.groupBoxInventory.TabIndex = 2;
            this.groupBoxInventory.TabStop = false;
            this.groupBoxInventory.Text = "المخزون";

            //
            // lblStock
            //
            this.lblStock.AutoSize = true;
            this.lblStock.Location = new System.Drawing.Point(15, 25);
            this.lblStock.Name = "lblStock";
            this.lblStock.Size = new System.Drawing.Size(40, 15);
            this.lblStock.TabIndex = 0;
            this.lblStock.Text = "الكمية:";

            //
            // numStock
            //
            this.numStock.Location = new System.Drawing.Point(15, 45);
            this.numStock.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.numStock.Name = "numStock";
            this.numStock.Size = new System.Drawing.Size(100, 23);
            this.numStock.TabIndex = 1;
            this.numStock.ValueChanged += new System.EventHandler(this.numStock_ValueChanged);

            //
            // lblLowStockThreshold
            //
            this.lblLowStockThreshold.AutoSize = true;
            this.lblLowStockThreshold.Location = new System.Drawing.Point(130, 25);
            this.lblLowStockThreshold.Name = "lblLowStockThreshold";
            this.lblLowStockThreshold.Size = new System.Drawing.Size(80, 15);
            this.lblLowStockThreshold.TabIndex = 2;
            this.lblLowStockThreshold.Text = "حد المخزون المنخفض:";

            //
            // numLowStockThreshold
            //
            this.numLowStockThreshold.Location = new System.Drawing.Point(130, 45);
            this.numLowStockThreshold.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
            this.numLowStockThreshold.Name = "numLowStockThreshold";
            this.numLowStockThreshold.Size = new System.Drawing.Size(100, 23);
            this.numLowStockThreshold.TabIndex = 3;
            this.numLowStockThreshold.Value = new decimal(new int[] { 10, 0, 0, 0 });
            this.numLowStockThreshold.ValueChanged += new System.EventHandler(this.numLowStockThreshold_ValueChanged);

            //
            // lblStockStatus
            //
            this.lblStockStatus.AutoSize = true;
            this.lblStockStatus.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lblStockStatus.ForeColor = System.Drawing.Color.Green;
            this.lblStockStatus.Location = new System.Drawing.Point(15, 80);
            this.lblStockStatus.Name = "lblStockStatus";
            this.lblStockStatus.Size = new System.Drawing.Size(90, 15);
            this.lblStockStatus.TabIndex = 4;
            this.lblStockStatus.Text = "حالة المخزون: متوفر";

            //
            // groupBoxClassification
            //
            this.groupBoxClassification.Controls.Add(this.lblCategory);
            this.groupBoxClassification.Controls.Add(this.cmbCategory);
            this.groupBoxClassification.Controls.Add(this.btnAddCategory);
            this.groupBoxClassification.Controls.Add(this.lblUnit);
            this.groupBoxClassification.Controls.Add(this.cmbUnit);
            this.groupBoxClassification.Controls.Add(this.btnAddUnit);
            this.groupBoxClassification.Controls.Add(this.chkIsActive);
            this.groupBoxClassification.Location = new System.Drawing.Point(12, 200);
            this.groupBoxClassification.Name = "groupBoxClassification";
            this.groupBoxClassification.Size = new System.Drawing.Size(400, 120);
            this.groupBoxClassification.TabIndex = 3;
            this.groupBoxClassification.TabStop = false;
            this.groupBoxClassification.Text = "التصنيف والوحدة";

            //
            // lblCategory
            //
            this.lblCategory.AutoSize = true;
            this.lblCategory.Location = new System.Drawing.Point(15, 25);
            this.lblCategory.Name = "lblCategory";
            this.lblCategory.Size = new System.Drawing.Size(50, 15);
            this.lblCategory.TabIndex = 0;
            this.lblCategory.Text = "التصنيف:";

            //
            // cmbCategory
            //
            this.cmbCategory.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCategory.FormattingEnabled = true;
            this.cmbCategory.Location = new System.Drawing.Point(15, 45);
            this.cmbCategory.Name = "cmbCategory";
            this.cmbCategory.Size = new System.Drawing.Size(150, 23);
            this.cmbCategory.TabIndex = 1;

            //
            // btnAddCategory
            //
            this.btnAddCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnAddCategory.ForeColor = System.Drawing.Color.White;
            this.btnAddCategory.Location = new System.Drawing.Point(175, 43);
            this.btnAddCategory.Name = "btnAddCategory";
            this.btnAddCategory.Size = new System.Drawing.Size(30, 27);
            this.btnAddCategory.TabIndex = 2;
            this.btnAddCategory.Text = "➕";
            this.btnAddCategory.UseVisualStyleBackColor = false;

            //
            // lblUnit
            //
            this.lblUnit.AutoSize = true;
            this.lblUnit.Location = new System.Drawing.Point(220, 25);
            this.lblUnit.Name = "lblUnit";
            this.lblUnit.Size = new System.Drawing.Size(60, 15);
            this.lblUnit.TabIndex = 3;
            this.lblUnit.Text = "وحدة القياس:";

            //
            // cmbUnit
            //
            this.cmbUnit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbUnit.FormattingEnabled = true;
            this.cmbUnit.Location = new System.Drawing.Point(220, 45);
            this.cmbUnit.Name = "cmbUnit";
            this.cmbUnit.Size = new System.Drawing.Size(120, 23);
            this.cmbUnit.TabIndex = 4;

            //
            // btnAddUnit
            //
            this.btnAddUnit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnAddUnit.ForeColor = System.Drawing.Color.White;
            this.btnAddUnit.Location = new System.Drawing.Point(350, 43);
            this.btnAddUnit.Name = "btnAddUnit";
            this.btnAddUnit.Size = new System.Drawing.Size(30, 27);
            this.btnAddUnit.TabIndex = 5;
            this.btnAddUnit.Text = "➕";
            this.btnAddUnit.UseVisualStyleBackColor = false;

            //
            // chkIsActive
            //
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Checked = true;
            this.chkIsActive.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkIsActive.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.chkIsActive.Location = new System.Drawing.Point(15, 85);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Size = new System.Drawing.Size(48, 19);
            this.chkIsActive.TabIndex = 6;
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.UseVisualStyleBackColor = true;

            //
            // btnSave
            //
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnSave.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.Location = new System.Drawing.Point(430, 280);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(120, 40);
            this.btnSave.TabIndex = 4;
            this.btnSave.Text = "💾 حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);

            //
            // btnCancel
            //
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(149)))), ((int)(((byte)(165)))), ((int)(((byte)(166)))));
            this.btnCancel.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(560, 280);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 40);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "❌ إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);

            //
            // AddEditProductForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(700, 340);
            this.Controls.Add(this.groupBoxBasicInfo);
            this.Controls.Add(this.groupBoxPricing);
            this.Controls.Add(this.groupBoxInventory);
            this.Controls.Add(this.groupBoxClassification);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditProductForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "إضافة/تعديل منتج";

            ((System.ComponentModel.ISupportInitialize)(this.numPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCost)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numStock)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLowStockThreshold)).EndInit();
            this.groupBoxBasicInfo.ResumeLayout(false);
            this.groupBoxBasicInfo.PerformLayout();
            this.groupBoxPricing.ResumeLayout(false);
            this.groupBoxPricing.PerformLayout();
            this.groupBoxInventory.ResumeLayout(false);
            this.groupBoxInventory.PerformLayout();
            this.groupBoxClassification.ResumeLayout(false);
            this.groupBoxClassification.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion
    }
}
