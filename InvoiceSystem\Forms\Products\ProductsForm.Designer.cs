namespace InvoiceSystem.Forms.Products
{
    partial class ProductsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            dgvProducts = new DataGridView();
            btnAdd = new Button();
            btnEdit = new Button();
            btnDelete = new Button();
            btnUpdateStock = new Button();
            btnClose = new Button();
            txtSearch = new TextBox();
            lblSearch = new Label();
            panel1 = new Panel();
            btnManageCategories = new Button();
            btnManageUnits = new Button();
            btnImport = new Button();
            btnExport = new Button();
            panel2 = new Panel();
            lblCategoryFilter = new Label();
            cmbCategoryFilter = new ComboBox();
            lblLowStockThreshold = new Label();
            numLowStockThreshold = new NumericUpDown();
            btnShowLowStock = new Button();
            btnShowAll = new Button();
            btnRefresh = new Button();
            lblTotalProducts = new Label();
            lblOutOfStock = new Label();
            lblLowStock = new Label();
            lblInStock = new Label();
            lblTotalValue = new Label();
            panel3 = new Panel();
            lblStatus = new Label();
            ((System.ComponentModel.ISupportInitialize)dgvProducts).BeginInit();
            panel1.SuspendLayout();
            panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numLowStockThreshold).BeginInit();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // dgvProducts
            // 
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvProducts.Location = new Point(12, 140);
            dgvProducts.MultiSelect = false;
            dgvProducts.Name = "dgvProducts";
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.Size = new Size(1000, 400);
            dgvProducts.TabIndex = 2;
            // 
            // btnAdd
            // 
            btnAdd.BackColor = Color.LightGreen;
            btnAdd.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnAdd.Location = new Point(340, 10);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(120, 40);
            btnAdd.TabIndex = 0;
            btnAdd.Text = "إضافة منتج";
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // btnEdit
            // 
            btnEdit.BackColor = Color.LightBlue;
            btnEdit.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnEdit.Location = new Point(466, 10);
            btnEdit.Name = "btnEdit";
            btnEdit.Size = new Size(120, 40);
            btnEdit.TabIndex = 1;
            btnEdit.Text = "تعديل";
            btnEdit.UseVisualStyleBackColor = false;
            btnEdit.Click += btnEdit_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.LightCoral;
            btnDelete.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnDelete.Location = new Point(613, 10);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(120, 40);
            btnDelete.TabIndex = 2;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdateStock
            // 
            btnUpdateStock.BackColor = Color.LightYellow;
            btnUpdateStock.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnUpdateStock.Location = new Point(748, 10);
            btnUpdateStock.Name = "btnUpdateStock";
            btnUpdateStock.Size = new Size(120, 40);
            btnUpdateStock.TabIndex = 3;
            btnUpdateStock.Text = "تحديث المخزون";
            btnUpdateStock.UseVisualStyleBackColor = false;
            btnUpdateStock.Click += btnUpdateStock_Click;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.LightGray;
            btnClose.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnClose.Location = new Point(870, 10);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(120, 40);
            btnClose.TabIndex = 4;
            btnClose.Text = "إغلاق";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += btnClose_Click;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Tahoma", 10F);
            txtSearch.Location = new Point(10, 28);
            txtSearch.Name = "txtSearch";
            txtSearch.Size = new Size(140, 24);
            txtSearch.TabIndex = 1;
            txtSearch.TextAlign = HorizontalAlignment.Center;
            txtSearch.TextChanged += txtSearch_TextChanged;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblSearch.Location = new Point(55, 9);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(43, 17);
            lblSearch.TabIndex = 0;
            lblSearch.Text = "بحث:";
            // 
            // panel1
            // 
            panel1.Controls.Add(btnAdd);
            panel1.Controls.Add(btnEdit);
            panel1.Controls.Add(btnDelete);
            panel1.Controls.Add(btnUpdateStock);
            panel1.Controls.Add(btnManageCategories);
            panel1.Controls.Add(btnManageUnits);
            panel1.Controls.Add(btnImport);
            panel1.Controls.Add(btnClose);
            panel1.Controls.Add(btnExport);
            panel1.Location = new Point(12, 560);
            panel1.Name = "panel1";
            panel1.Size = new Size(1000, 113);
            panel1.TabIndex = 3;
            // 
            // btnManageCategories
            // 
            btnManageCategories.BackColor = Color.FromArgb(52, 152, 219);
            btnManageCategories.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnManageCategories.ForeColor = Color.White;
            btnManageCategories.Location = new Point(10, 9);
            btnManageCategories.Name = "btnManageCategories";
            btnManageCategories.Size = new Size(151, 40);
            btnManageCategories.TabIndex = 5;
            btnManageCategories.Text = "🏷️ إدارة التصنيفات";
            btnManageCategories.UseVisualStyleBackColor = false;
            btnManageCategories.Click += btnManageCategories_Click;
            // 
            // btnManageUnits
            // 
            btnManageUnits.BackColor = Color.FromArgb(155, 89, 182);
            btnManageUnits.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnManageUnits.ForeColor = Color.White;
            btnManageUnits.Location = new Point(180, 9);
            btnManageUnits.Name = "btnManageUnits";
            btnManageUnits.Size = new Size(156, 40);
            btnManageUnits.TabIndex = 6;
            btnManageUnits.Text = "📏 إدارة الوحدات";
            btnManageUnits.UseVisualStyleBackColor = false;
            btnManageUnits.Click += btnManageUnits_Click;
            // 
            // btnImport
            // 
            btnImport.BackColor = Color.FromArgb(230, 126, 34);
            btnImport.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnImport.ForeColor = Color.White;
            btnImport.Location = new Point(870, 56);
            btnImport.Name = "btnImport";
            btnImport.Size = new Size(120, 40);
            btnImport.TabIndex = 8;
            btnImport.Text = "📥 استيراد";
            btnImport.UseVisualStyleBackColor = false;
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(241, 196, 15);
            btnExport.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnExport.ForeColor = Color.White;
            btnExport.Location = new Point(748, 56);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(120, 40);
            btnExport.TabIndex = 7;
            btnExport.Text = "📤 تصدير";
            btnExport.UseVisualStyleBackColor = false;
            // 
            // panel2
            // 
            panel2.Controls.Add(lblSearch);
            panel2.Controls.Add(txtSearch);
            panel2.Controls.Add(lblCategoryFilter);
            panel2.Controls.Add(cmbCategoryFilter);
            panel2.Controls.Add(lblLowStockThreshold);
            panel2.Controls.Add(numLowStockThreshold);
            panel2.Controls.Add(btnShowLowStock);
            panel2.Controls.Add(btnShowAll);
            panel2.Controls.Add(btnRefresh);
            panel2.Location = new Point(12, 12);
            panel2.Name = "panel2";
            panel2.Size = new Size(1000, 60);
            panel2.TabIndex = 0;
            // 
            // lblCategoryFilter
            // 
            lblCategoryFilter.AutoSize = true;
            lblCategoryFilter.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblCategoryFilter.Location = new Point(443, 5);
            lblCategoryFilter.Name = "lblCategoryFilter";
            lblCategoryFilter.Size = new Size(66, 17);
            lblCategoryFilter.TabIndex = 6;
            lblCategoryFilter.Text = "التصنيف:";
            // 
            // cmbCategoryFilter
            // 
            cmbCategoryFilter.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCategoryFilter.Font = new Font("Tahoma", 10F);
            cmbCategoryFilter.FormattingEnabled = true;
            cmbCategoryFilter.Location = new Point(393, 30);
            cmbCategoryFilter.Name = "cmbCategoryFilter";
            cmbCategoryFilter.Size = new Size(203, 24);
            cmbCategoryFilter.TabIndex = 7;
            // 
            // lblLowStockThreshold
            // 
            lblLowStockThreshold.AutoSize = true;
            lblLowStockThreshold.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblLowStockThreshold.Location = new Point(153, 5);
            lblLowStockThreshold.Name = "lblLowStockThreshold";
            lblLowStockThreshold.Size = new Size(157, 17);
            lblLowStockThreshold.TabIndex = 2;
            lblLowStockThreshold.Text = "حد المخزون المنخفض:";
            // 
            // numLowStockThreshold
            // 
            numLowStockThreshold.Font = new Font("Tahoma", 10F);
            numLowStockThreshold.Location = new Point(180, 30);
            numLowStockThreshold.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numLowStockThreshold.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numLowStockThreshold.Name = "numLowStockThreshold";
            numLowStockThreshold.Size = new Size(80, 24);
            numLowStockThreshold.TabIndex = 3;
            numLowStockThreshold.TextAlign = HorizontalAlignment.Center;
            numLowStockThreshold.Value = new decimal(new int[] { 10, 0, 0, 0 });
            numLowStockThreshold.ValueChanged += numLowStockThreshold_ValueChanged;
            // 
            // btnShowLowStock
            // 
            btnShowLowStock.BackColor = Color.LightYellow;
            btnShowLowStock.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnShowLowStock.Location = new Point(630, 22);
            btnShowLowStock.Name = "btnShowLowStock";
            btnShowLowStock.Size = new Size(120, 30);
            btnShowLowStock.TabIndex = 4;
            btnShowLowStock.Text = "مخزون منخفض";
            btnShowLowStock.UseVisualStyleBackColor = false;
            btnShowLowStock.Click += btnShowLowStock_Click;
            // 
            // btnShowAll
            // 
            btnShowAll.BackColor = Color.LightBlue;
            btnShowAll.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnShowAll.Location = new Point(768, 15);
            btnShowAll.Name = "btnShowAll";
            btnShowAll.Size = new Size(100, 30);
            btnShowAll.TabIndex = 5;
            btnShowAll.Text = "عرض الكل";
            btnShowAll.UseVisualStyleBackColor = false;
            btnShowAll.Click += btnShowAll_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(46, 204, 113);
            btnRefresh.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(904, 15);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(80, 30);
            btnRefresh.TabIndex = 8;
            btnRefresh.Text = "🔄 تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            // 
            // lblTotalProducts
            // 
            lblTotalProducts.AutoSize = true;
            lblTotalProducts.BackColor = Color.LightBlue;
            lblTotalProducts.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblTotalProducts.Location = new Point(153, 15);
            lblTotalProducts.Name = "lblTotalProducts";
            lblTotalProducts.Padding = new Padding(5);
            lblTotalProducts.Size = new Size(147, 27);
            lblTotalProducts.TabIndex = 0;
            lblTotalProducts.Text = "إجمالي المنتجات: 0";
            // 
            // lblOutOfStock
            // 
            lblOutOfStock.AutoSize = true;
            lblOutOfStock.BackColor = Color.LightCoral;
            lblOutOfStock.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblOutOfStock.Location = new Point(329, 15);
            lblOutOfStock.Name = "lblOutOfStock";
            lblOutOfStock.Padding = new Padding(5);
            lblOutOfStock.Size = new Size(115, 27);
            lblOutOfStock.TabIndex = 1;
            lblOutOfStock.Text = "نفد المخزون: 0";
            // 
            // lblLowStock
            // 
            lblLowStock.AutoSize = true;
            lblLowStock.BackColor = Color.LightYellow;
            lblLowStock.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblLowStock.Location = new Point(459, 15);
            lblLowStock.Name = "lblLowStock";
            lblLowStock.Padding = new Padding(5);
            lblLowStock.Size = new Size(137, 27);
            lblLowStock.TabIndex = 2;
            lblLowStock.Text = "مخزون منخفض: 0";
            // 
            // lblInStock
            // 
            lblInStock.AutoSize = true;
            lblInStock.BackColor = Color.LightGreen;
            lblInStock.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblInStock.Location = new Point(626, 15);
            lblInStock.Name = "lblInStock";
            lblInStock.Padding = new Padding(5);
            lblInStock.Size = new Size(72, 27);
            lblInStock.TabIndex = 3;
            lblInStock.Text = "متوفر: 0";
            // 
            // lblTotalValue
            // 
            lblTotalValue.AutoSize = true;
            lblTotalValue.BackColor = Color.LightGoldenrodYellow;
            lblTotalValue.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblTotalValue.Location = new Point(726, 15);
            lblTotalValue.Name = "lblTotalValue";
            lblTotalValue.Padding = new Padding(5);
            lblTotalValue.Size = new Size(125, 27);
            lblTotalValue.TabIndex = 4;
            lblTotalValue.Text = "قيمة المخزون: 0";
            // 
            // panel3
            // 
            panel3.Controls.Add(lblTotalProducts);
            panel3.Controls.Add(lblOutOfStock);
            panel3.Controls.Add(lblLowStock);
            panel3.Controls.Add(lblInStock);
            panel3.Controls.Add(lblTotalValue);
            panel3.Location = new Point(12, 80);
            panel3.Name = "panel3";
            panel3.Size = new Size(1000, 50);
            panel3.TabIndex = 1;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Tahoma", 9F);
            lblStatus.ForeColor = Color.Blue;
            lblStatus.Location = new Point(12, 676);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(32, 14);
            lblStatus.TabIndex = 9;
            lblStatus.Text = "جاهز";
            // 
            // ProductsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1024, 699);
            Controls.Add(panel1);
            Controls.Add(dgvProducts);
            Controls.Add(panel3);
            Controls.Add(panel2);
            Controls.Add(lblStatus);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ProductsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "إدارة المنتجات";
            ((System.ComponentModel.ISupportInitialize)dgvProducts).EndInit();
            panel1.ResumeLayout(false);
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numLowStockThreshold).EndInit();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        private System.Windows.Forms.DataGridView dgvProducts;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnUpdateStock;
        private System.Windows.Forms.Button btnClose;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label lblTotalProducts;
        private System.Windows.Forms.Label lblOutOfStock;
        private System.Windows.Forms.Label lblLowStock;
        private System.Windows.Forms.Label lblInStock;
        private System.Windows.Forms.Label lblTotalValue;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Button btnShowLowStock;
        private System.Windows.Forms.Button btnShowAll;
        private System.Windows.Forms.Label lblLowStockThreshold;
        private System.Windows.Forms.NumericUpDown numLowStockThreshold;
        private System.Windows.Forms.ComboBox cmbCategoryFilter;
        private System.Windows.Forms.Label lblCategoryFilter;
        private System.Windows.Forms.Button btnManageCategories;
        private System.Windows.Forms.Button btnManageUnits;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImport;
    }
}
