using InvoiceSystem.Forms;
using InvoiceSystem.Services;
using InvoiceSystem.Data;
using System.Threading.Tasks;
using System.IO;
using Dapper;
using InvoiceSystem.Forms.Accounting;
using InvoiceSystem.Forms.Vouchers;
using InvoiceSystem.Data.SimpleORM.Test;
using InvoiceSystem.Forms.Products;

namespace InvoiceSystem;

public partial class MainForm : Form
{
    private readonly DatabaseHelper dbHelper;

    public MainForm()
    {
        InitializeComponent();
        this.Text = "نظام إدارة الفواتير والمحاسبة";
        //this.WindowState = FormWindowState.Maximized;
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;

        dbHelper = new DatabaseHelper();

        // التحقق من وجود البيانات التجريبية وإضافتها إذا لم تكن موجودة
        CheckAndSeedDataIfNeeded();
    }

    private void btnCustomers_Click(object sender, EventArgs e)
    {
        var customerService = new CustomerService(dbHelper);
        var customersForm = new CustomersForm(customerService);
        customersForm.ShowDialog();
    }

    private void btnProducts_Click(object sender, EventArgs e)
    {
        var productService = new ProductService(dbHelper);
        var productsForm = new ProductsForm(productService);
        productsForm.ShowDialog();
    }

    private void btnInvoices_Click(object sender, EventArgs e)
    {
        var integrationService = new IntegrationService(dbHelper);
        var customerService = new CustomerService(dbHelper);
        var productService = new ProductService(dbHelper);
        var invoiceService = integrationService.CreateIntegratedInvoiceService();
        var paymentService = integrationService.CreateIntegratedPaymentService();
        var invoicesForm = new InvoicesForm(invoiceService, customerService, productService, paymentService);
        invoicesForm.ShowDialog();
    }

    private void btnReports_Click(object sender, EventArgs e)
    {
        var customerService = new CustomerService(dbHelper);
        var productService = new ProductService(dbHelper);
        var invoiceService = new InvoiceService(dbHelper);
        var paymentService = new PaymentService(dbHelper);
        var expenseService = new ExpenseService(dbHelper);
        var reportsForm = new ReportsForm(customerService, productService, invoiceService, paymentService, expenseService);
        reportsForm.ShowDialog();
    }

    private void btnExpenses_Click(object sender, EventArgs e)
    {
        var integrationService = new IntegrationService(dbHelper);
        var expenseService = integrationService.CreateIntegratedExpenseService();
        var expensesForm = new ExpensesForm(expenseService);
        expensesForm.ShowDialog();
    }

    private void btnAccounting_Click(object sender, EventArgs e)
    {
        var accountService = new AccountService(dbHelper);
        var chartForm = new SimpleChartOfAccountsForm(accountService);
        var chartForm2 = new ChartOfAccountsForm(accountService);
        chartForm.ShowDialog();
        //chartForm2.ShowDialog();
    }

    private void btnJournalEntries_Click(object sender, EventArgs e)
    {
        var accountService = new AccountService(dbHelper);
        var journalService = new JournalEntryService(dbHelper, accountService);
        var journalForm = new JournalEntriesForm(journalService, accountService);
        journalForm.ShowDialog();
    }

    private void btnJournalManagement_Click(object sender, EventArgs e)
    {
        var accountService = new AccountService(dbHelper);
        var journalService = new JournalEntryService(dbHelper, accountService);
        var journalManagementForm = new JournalEntriesManagementFormDesigner(journalService, accountService);
        journalManagementForm.ShowDialog();
    }

    private void btnVouchers_Click(object sender, EventArgs e)
    {
        try
        {
            // فتح نافذة إدارة السندات الجديدة
            var vouchersManagementForm = new VouchersManagementForm();
            vouchersManagementForm.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إدارة السندات:\n{ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnVouchersOld_Click(object sender, EventArgs e)
    {
        // النظام القديم للسندات (للمقارنة)
        var accountService = new AccountService(dbHelper);
        var journalService = new JournalEntryService(dbHelper, accountService);
        var voucherService = new VoucherService(dbHelper);
        var vouchersForm = new VouchersForm(voucherService, accountService, journalService);
        vouchersForm.ShowDialog();
    }

    private void btnIntegration_Click(object sender, EventArgs e)
    {
        var integrationService = new IntegrationService(dbHelper);
        var integrationForm = new AccountingIntegrationForm(integrationService);
        integrationForm.ShowDialog();
    }

    private void btnTestVouchers_Click(object sender, EventArgs e)
    {
        // فتح نموذج اختبار السندات الجديد
        var testForm = new TestVouchersForm();
        testForm.ShowDialog();
    }

    private async void btnSeedData_Click(object sender, EventArgs e)
    {
        try
        {
            var result = MessageBox.Show(
                "هل تريد إضافة البيانات التجريبية؟\n\n" +
                "سيتم إضافة:\n" +
                "• دليل محاسبي شامل\n" +
                "• عملاء تجريبيين\n" +
                "• منتجات متنوعة\n" +
                "• فواتير ومبيعات\n" +
                "• نفقات ومصروفات\n" +
                "• قيود يومية\n\n" +
                "تنبيه: إذا كانت البيانات موجودة مسبقاً، لن يتم إضافة بيانات جديدة.",
                "إضافة بيانات تجريبية",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // إظهار رسالة تحميل
                var loadingForm = new Form
                {
                    Text = "جاري إضافة البيانات...",
                    Size = new Size(400, 150),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false,
                    RightToLeft = RightToLeft.Yes,
                    RightToLeftLayout = true
                };

                var lblLoading = new Label
                {
                    Text = "جاري إضافة البيانات التجريبية...\nيرجى الانتظار",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Tahoma", 12, FontStyle.Bold)
                };

                loadingForm.Controls.Add(lblLoading);
                loadingForm.Show();
                Application.DoEvents();

                // إضافة البيانات
                await Task.Run(() =>
                {
                    var seeder = new DatabaseSeeder(dbHelper);
                    seeder.SeedDatabase();
                });

                loadingForm.Close();

                MessageBox.Show(
                    "تم إضافة البيانات التجريبية بنجاح!\n\n" +
                    "يمكنك الآن استكشاف النظام باستخدام البيانات التجريبية.",
                    "نجح",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"حدث خطأ أثناء إضافة البيانات التجريبية:\n{ex.Message}",
                "خطأ",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void CheckAndSeedDataIfNeeded()
    {
        try
        {
            using var connection = dbHelper.GetConnection();
            connection.Open();

            // التحقق من وجود الحسابات
            var accountsCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Accounts");

            if (accountsCount == 0)
            {
                // إضافة البيانات التجريبية تلقائياً
                var seeder = new DatabaseSeeder(dbHelper);
                seeder.SeedDatabase();

                // إظهار رسالة للمستخدم
                MessageBox.Show(
                    "تم إضافة البيانات التجريبية تلقائياً!\n\n" +
                    "يمكنك الآن استكشاف جميع ميزات النظام باستخدام البيانات التجريبية المضافة.",
                    "البيانات التجريبية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            // في حالة حدوث خطأ، لا نعرض رسالة خطأ لتجنب إزعاج المستخدم
            // يمكن للمستخدم استخدام الزر لإضافة البيانات يدوياً
            Console.WriteLine($"خطأ في إضافة البيانات التجريبية تلقائياً: {ex.Message}");
        }
    }

    private async void btnTestORM_Click(object sender, EventArgs e)
    {
        try
        {
            // إظهار نافذة الاختبار
            var testForm = new Form
            {
                Text = "اختبار مكتبة SimpleORM",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            var txtOutput = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 10),
                ReadOnly = true,
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            testForm.Controls.Add(txtOutput);

            // تشغيل الاختبار في الخلفية
            testForm.Show();
            txtOutput.Text = "جاري تشغيل اختبارات SimpleORM...\r\n\r\n";
            Application.DoEvents();

            // إعادة توجيه Console.WriteLine إلى TextBox
            var originalOut = Console.Out;
            var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            try
            {
                await SimpleORMTest.RunTestAsync();
            }
            finally
            {
                Console.SetOut(originalOut);
            }

            txtOutput.Text += stringWriter.ToString();
            txtOutput.SelectionStart = txtOutput.Text.Length;
            txtOutput.ScrollToCaret();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل اختبار SimpleORM:\n{ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnPartners_Click(object sender, EventArgs e)
    {
        try
        {
            var partnerForm = new PartnerManagementForm();
            partnerForm.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إدارة الشركاء:\n{ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void btnSeedPartners_Click(object sender, EventArgs e)
    {
        try
        {
            var result = MessageBox.Show(
                "هل تريد إنشاء بيانات تجريبية للشركاء؟\n\nسيتم إضافة عملاء وموردين وموظفين وجهات اتصال تجريبية.",
                "إنشاء بيانات تجريبية",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                var dbHelper = new DatabaseHelper();
                var partnerService = new PartnerService(dbHelper);

                await PartnerSeedData.SeedPartnersAsync(partnerService);

                MessageBox.Show("تم إنشاء البيانات التجريبية للشركاء بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء البيانات التجريبية:\n{ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnTestDirectORM_Click(object sender, EventArgs e)
    {
        try
        {
            var testForm = new TestDirectORMForm();
            testForm.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة اختبار DirectORM:\n{ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
