using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Threading.Tasks;
using InvoiceSystem.Database;

namespace InvoiceSystem.Data
{
    public class DatabaseHelper
    {
        private readonly string _connectionString;

        public DatabaseHelper()
        {
            _connectionString = "Data Source=invoice_system.db;Version=3;";
            InitializeDatabase();
        }

        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        private void InitializeDatabase()
        {
            using var connection = GetConnection();
            connection.Open();

            // Create Customers table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Address TEXT,
                    Balance DECIMAL DEFAULT 0,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                )");

            // Create Product Categories table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS ProductCategories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    Color TEXT DEFAULT '#007ACC',
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME
                )");

            // Create Units of Measure table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS UnitsOfMeasure (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Symbol TEXT,
                    Description TEXT,
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                )");

            // Create Products table (enhanced)
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    Price DECIMAL NOT NULL,
                    Cost DECIMAL DEFAULT 0,
                    Stock INTEGER DEFAULT 0,
                    LowStockThreshold INTEGER DEFAULT 10,
                    Barcode TEXT,
                    CategoryId INTEGER,
                    UnitOfMeasureId INTEGER,
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME,
                    FOREIGN KEY (CategoryId) REFERENCES ProductCategories(Id),
                    FOREIGN KEY (UnitOfMeasureId) REFERENCES UnitsOfMeasure(Id)
                )");

            // Create Product Movements table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS ProductMovements (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    Type INTEGER NOT NULL,
                    Quantity INTEGER NOT NULL,
                    UnitPrice DECIMAL NOT NULL,
                    TotalAmount DECIMAL NOT NULL,
                    Reference TEXT,
                    Notes TEXT,
                    MovementDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )");

            // Create Invoices table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Invoices (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerId INTEGER NOT NULL,
                    Date DATETIME NOT NULL,
                    Total DECIMAL NOT NULL,
                    PaidAmount DECIMAL DEFAULT 0,
                    Status INTEGER DEFAULT 0,
                    PaymentMethod INTEGER DEFAULT 0,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                )");

            // Create InvoiceItems table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS InvoiceItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity INTEGER NOT NULL,
                    Price DECIMAL NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )");

            // Create Payments table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Payments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceId INTEGER NOT NULL,
                    Amount DECIMAL NOT NULL,
                    Date DATETIME NOT NULL,
                    PaymentMethod INTEGER NOT NULL,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id)
                )");

            // Create CustomerTransactions table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS CustomerTransactions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerId INTEGER NOT NULL,
                    Type INTEGER NOT NULL,
                    Amount DECIMAL NOT NULL,
                    Date DATETIME NOT NULL,
                    Description TEXT,
                    InvoiceId INTEGER,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id)
                );

                -- Create ExpenseCategories table
                CREATE TABLE IF NOT EXISTS ExpenseCategories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Description TEXT,
                    Color TEXT DEFAULT '#FFFFFF',
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                -- Create Expenses table
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Title TEXT NOT NULL,
                    Description TEXT,
                    Amount DECIMAL(10,2) NOT NULL,
                    Date DATETIME NOT NULL,
                    CategoryId INTEGER NOT NULL,
                    PaymentMethod TEXT NOT NULL,
                    ReceiptNumber TEXT,
                    Vendor TEXT,
                    IsRecurring BOOLEAN DEFAULT 0,
                    RecurringType INTEGER,
                    NextDueDate DATETIME,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME,
                    FOREIGN KEY (CategoryId) REFERENCES ExpenseCategories(Id)
                );

                -- Insert default expense categories if not exists
                INSERT OR IGNORE INTO ExpenseCategories (Name, Description, Color) VALUES
                ('مصاريف إدارية', 'المصاريف الإدارية والتشغيلية', '#FF6B6B'),
                ('مصاريف تسويق', 'مصاريف الإعلان والتسويق', '#4ECDC4'),
                ('مصاريف مكتبية', 'القرطاسية والمستلزمات المكتبية', '#45B7D1'),
                ('إيجارات', 'إيجار المكاتب والمحلات', '#96CEB4'),
                ('رواتب وأجور', 'رواتب الموظفين والأجور', '#FFEAA7'),
                ('صيانة وإصلاح', 'صيانة المعدات والإصلاحات', '#DDA0DD'),
                ('مصاريف سفر', 'مصاريف السفر والانتقال', '#98D8C8'),
                ('فواتير خدمات', 'الكهرباء والماء والهاتف', '#F7DC6F'),
                ('مصاريف أخرى', 'مصاريف متنوعة', '#AED6F1');

                -- Create Accounts table (Chart of Accounts)
                CREATE TABLE IF NOT EXISTS Accounts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL UNIQUE,
                    Name TEXT NOT NULL,
                    NameEnglish TEXT,
                    Type INTEGER NOT NULL,
                    Nature INTEGER NOT NULL,
                    ParentId INTEGER,
                    Level INTEGER NOT NULL DEFAULT 1,
                    IsParent BOOLEAN DEFAULT 0,
                    IsActive BOOLEAN DEFAULT 1,
                    Balance DECIMAL(15,2) DEFAULT 0,
                    Description TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME,
                    FOREIGN KEY (ParentId) REFERENCES Accounts(Id)
                );

                -- Create JournalEntries table
                CREATE TABLE IF NOT EXISTS JournalEntries (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EntryNumber TEXT NOT NULL UNIQUE,
                    Date DATETIME NOT NULL,
                    Description TEXT NOT NULL,
                    Reference TEXT,
                    Type INTEGER NOT NULL,
                    Status INTEGER DEFAULT 1,
                    TotalDebit DECIMAL(15,2) DEFAULT 0,
                    TotalCredit DECIMAL(15,2) DEFAULT 0,
                    SourceId INTEGER,
                    SourceType TEXT,
                    CreatedBy TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedBy TEXT,
                    UpdatedDate DATETIME,
                    ApprovedBy TEXT,
                    ApprovedDate DATETIME,
                    Notes TEXT
                );

                -- Create JournalEntryDetails table
                CREATE TABLE IF NOT EXISTS JournalEntryDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    JournalEntryId INTEGER NOT NULL,
                    AccountId INTEGER NOT NULL,
                    DebitAmount DECIMAL(15,2) DEFAULT 0,
                    CreditAmount DECIMAL(15,2) DEFAULT 0,
                    Description TEXT,
                    LineNumber INTEGER DEFAULT 1,
                    FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id) ON DELETE CASCADE,
                    FOREIGN KEY (AccountId) REFERENCES Accounts(Id)
                );

                -- Create Vouchers table
                CREATE TABLE IF NOT EXISTS Vouchers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    VoucherNumber TEXT NOT NULL,
                    Type INTEGER NOT NULL,
                    Date DATETIME NOT NULL,
                    Description TEXT NOT NULL,
                    Amount DECIMAL(15,2) NOT NULL,
                    PayeeName TEXT,
                    PayeeType TEXT,
                    PayeeId INTEGER,
                    Status INTEGER DEFAULT 1,
                    Reference TEXT,
                    JournalEntryId INTEGER,
                    CreatedBy TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedBy TEXT,
                    UpdatedDate DATETIME,
                    ApprovedBy TEXT,
                    ApprovedDate DATETIME,
                    Notes TEXT,
                    FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id)
                );

                -- Create Partners table (Unified table for customers, suppliers, employees, contacts)
                CREATE TABLE IF NOT EXISTS Partners (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL UNIQUE,
                    Name TEXT NOT NULL,
                    NameEn TEXT,
                    PartnerType INTEGER NOT NULL,
                    Email TEXT,
                    Phone TEXT,
                    Mobile TEXT,
                    Fax TEXT,
                    Website TEXT,
                    Address TEXT,
                    City TEXT,
                    Country TEXT,
                    PostalCode TEXT,
                    TaxNumber TEXT,
                    CommercialRegister TEXT,
                    BankName TEXT,
                    BankAccount TEXT,
                    IBAN TEXT,
                    CreditLimit DECIMAL(15,2) DEFAULT 0,
                    PaymentTerms INTEGER DEFAULT 0,
                    Discount DECIMAL(5,2) DEFAULT 0,
                    IsActive BOOLEAN DEFAULT 1,
                    Notes TEXT,
                    Tags TEXT,
                    ContactPerson TEXT,
                    ContactTitle TEXT,
                    Department TEXT,
                    Position TEXT,
                    Salary DECIMAL(15,2) DEFAULT 0,
                    HireDate DATETIME,
                    BirthDate DATETIME,
                    NationalId TEXT,
                    PassportNumber TEXT,
                    EmergencyContact TEXT,
                    EmergencyPhone TEXT,
                    AccountId INTEGER,
                    ParentId INTEGER,
                    ImagePath TEXT,
                    AttachmentsPath TEXT,
                    CreatedBy TEXT DEFAULT 'System',
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME,
                    UpdatedBy TEXT,
                    IsDeleted BOOLEAN DEFAULT 0,
                    DeletedDate DATETIME,
                    DeletedBy TEXT,
                    FOREIGN KEY (AccountId) REFERENCES Accounts(Id),
                    FOREIGN KEY (ParentId) REFERENCES Partners(Id)
                );

                -- Create indexes for Partners table
                CREATE INDEX IF NOT EXISTS IX_Partners_Code ON Partners(Code);
                CREATE INDEX IF NOT EXISTS IX_Partners_Name ON Partners(Name);
                CREATE INDEX IF NOT EXISTS IX_Partners_Email ON Partners(Email);
                CREATE INDEX IF NOT EXISTS IX_Partners_PartnerType ON Partners(PartnerType);
                CREATE INDEX IF NOT EXISTS IX_Partners_TaxNumber ON Partners(TaxNumber);");

            // تحديث هيكل جدول الشركاء إذا لزم الأمر
            UpdatePartnersTableStructure();

            // إنشاء جداول الأمان والمستخدمين
            CreateSecurityTables();
        }

        /// <summary>
        /// تحديث هيكل جدول الشركاء لإضافة الأعمدة المفقودة
        /// </summary>
        private void UpdatePartnersTableStructure()
        {
            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
            var columnsToAdd = new Dictionary<string, string>
            {
                { "CreatedBy", "TEXT DEFAULT 'System'" },
                { "UpdatedBy", "TEXT" },
                { "IsDeleted", "BOOLEAN DEFAULT 0" },
                { "DeletedDate", "DATETIME" },
                { "DeletedBy", "TEXT" }
            };

            foreach (var column in columnsToAdd)
            {
                try
                {
                    // محاولة إضافة العمود إذا لم يكن موجوداً
                    using var command = connection.CreateCommand();
                    command.CommandText = $"ALTER TABLE Partners ADD COLUMN {column.Key} {column.Value}";
                    command.ExecuteNonQuery();
                }
                catch (SQLiteException ex)
                {
                    // تجاهل الخطأ إذا كان العمود موجوداً بالفعل
                    if (!ex.Message.Contains("duplicate column name"))
                    {
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// إنشاء جداول الأمان والمستخدمين
        /// </summary>
        private void CreateSecurityTables()
        {
            using var connection = GetConnection();
            connection.Open();

            // Create Users table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    Email TEXT NOT NULL UNIQUE,
                    PasswordHash TEXT NOT NULL,
                    Salt TEXT NOT NULL,
                    FirstName TEXT NOT NULL,
                    LastName TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Phone TEXT,
                    Department TEXT,
                    Position TEXT,
                    IsActive BOOLEAN DEFAULT 1,
                    IsLocked BOOLEAN DEFAULT 0,
                    FailedLoginAttempts INTEGER DEFAULT 0,
                    LastLoginDate DATETIME,
                    LastPasswordChangeDate DATETIME,
                    PasswordExpiryDate DATETIME,
                    MustChangePassword BOOLEAN DEFAULT 0,
                    TwoFactorEnabled BOOLEAN DEFAULT 0,
                    TwoFactorSecret TEXT,
                    ProfileImagePath TEXT,
                    Notes TEXT,
                    CreatedBy INTEGER,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ModifiedBy INTEGER,
                    ModifiedDate DATETIME
                );

                CREATE INDEX IF NOT EXISTS IX_Users_Username ON Users(Username);
                CREATE INDEX IF NOT EXISTS IX_Users_Email ON Users(Email);
                CREATE INDEX IF NOT EXISTS IX_Users_IsActive ON Users(IsActive);");

            // Create Roles table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Roles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    DisplayName TEXT NOT NULL,
                    Description TEXT,
                    IsSystemRole BOOLEAN DEFAULT 0,
                    IsActive BOOLEAN DEFAULT 1,
                    Priority INTEGER DEFAULT 0,
                    Color TEXT,
                    Icon TEXT,
                    CreatedBy INTEGER,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ModifiedBy INTEGER,
                    ModifiedDate DATETIME
                );

                CREATE INDEX IF NOT EXISTS IX_Roles_Name ON Roles(Name);
                CREATE INDEX IF NOT EXISTS IX_Roles_IsActive ON Roles(IsActive);");

            // Create Permissions table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Permissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    DisplayName TEXT NOT NULL,
                    Description TEXT,
                    Category TEXT NOT NULL,
                    Module TEXT NOT NULL,
                    Action TEXT NOT NULL,
                    Resource TEXT,
                    IsSystemPermission BOOLEAN DEFAULT 0,
                    IsActive BOOLEAN DEFAULT 1,
                    Priority INTEGER DEFAULT 0,
                    Icon TEXT,
                    CreatedBy INTEGER,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ModifiedBy INTEGER,
                    ModifiedDate DATETIME
                );

                CREATE INDEX IF NOT EXISTS IX_Permissions_Name ON Permissions(Name);
                CREATE INDEX IF NOT EXISTS IX_Permissions_Category ON Permissions(Category);
                CREATE INDEX IF NOT EXISTS IX_Permissions_Module ON Permissions(Module);");

            // Create UserRoles table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS UserRoles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    RoleId INTEGER NOT NULL,
                    AssignedBy INTEGER,
                    AssignedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ExpiryDate DATETIME,
                    IsActive BOOLEAN DEFAULT 1,
                    Notes TEXT,
                    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
                    FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
                    UNIQUE(UserId, RoleId)
                );

                CREATE INDEX IF NOT EXISTS IX_UserRoles_UserId ON UserRoles(UserId);
                CREATE INDEX IF NOT EXISTS IX_UserRoles_RoleId ON UserRoles(RoleId);");

            // Create RolePermissions table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS RolePermissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RoleId INTEGER NOT NULL,
                    PermissionId INTEGER NOT NULL,
                    GrantedBy INTEGER,
                    GrantedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    IsActive BOOLEAN DEFAULT 1,
                    Notes TEXT,
                    FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
                    FOREIGN KEY (PermissionId) REFERENCES Permissions(Id) ON DELETE CASCADE,
                    UNIQUE(RoleId, PermissionId)
                );

                CREATE INDEX IF NOT EXISTS IX_RolePermissions_RoleId ON RolePermissions(RoleId);
                CREATE INDEX IF NOT EXISTS IX_RolePermissions_PermissionId ON RolePermissions(PermissionId);");

            // Create UserPermissions table (for direct user permissions)
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS UserPermissions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    PermissionId INTEGER NOT NULL,
                    IsGranted BOOLEAN NOT NULL DEFAULT 1,
                    GrantedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    ExpiryDate DATETIME,
                    Reason TEXT,
                    GrantedByUserId INTEGER,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Priority INTEGER NOT NULL DEFAULT 0,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
                    FOREIGN KEY (PermissionId) REFERENCES Permissions(Id) ON DELETE CASCADE,
                    FOREIGN KEY (GrantedByUserId) REFERENCES Users(Id),
                    UNIQUE(UserId, PermissionId)
                );

                CREATE INDEX IF NOT EXISTS IX_UserPermissions_UserId ON UserPermissions(UserId);
                CREATE INDEX IF NOT EXISTS IX_UserPermissions_PermissionId ON UserPermissions(PermissionId);
                CREATE INDEX IF NOT EXISTS IX_UserPermissions_ExpiryDate ON UserPermissions(ExpiryDate);");

            // Update UserPermissions table structure if needed
            UpdateUserPermissionsTableStructure();

            // Create AuditLogs table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS AuditLogs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER,
                    Username TEXT,
                    Action TEXT NOT NULL,
                    EntityType TEXT NOT NULL,
                    EntityId INTEGER,
                    EntityName TEXT,
                    OldValues TEXT,
                    NewValues TEXT,
                    Changes TEXT,
                    IPAddress TEXT,
                    UserAgent TEXT,
                    SessionId TEXT,
                    Module TEXT NOT NULL,
                    Severity INTEGER NOT NULL DEFAULT 1,
                    Success BOOLEAN DEFAULT 1,
                    ErrorMessage TEXT,
                    Duration INTEGER,
                    AdditionalData TEXT,
                    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );

                CREATE INDEX IF NOT EXISTS IX_AuditLogs_UserId ON AuditLogs(UserId);
                CREATE INDEX IF NOT EXISTS IX_AuditLogs_Action ON AuditLogs(Action);
                CREATE INDEX IF NOT EXISTS IX_AuditLogs_EntityType ON AuditLogs(EntityType);
                CREATE INDEX IF NOT EXISTS IX_AuditLogs_Timestamp ON AuditLogs(Timestamp);
                CREATE INDEX IF NOT EXISTS IX_AuditLogs_Module ON AuditLogs(Module);");

            // Create UserSessions table
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS UserSessions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    SessionId TEXT NOT NULL UNIQUE,
                    IPAddress TEXT,
                    UserAgent TEXT,
                    LoginTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastActivityTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LogoutTime DATETIME,
                    IsActive BOOLEAN DEFAULT 1,
                    LogoutReason TEXT,
                    DeviceInfo TEXT,
                    Location TEXT,
                    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
                );

                CREATE INDEX IF NOT EXISTS IX_UserSessions_UserId ON UserSessions(UserId);
                CREATE INDEX IF NOT EXISTS IX_UserSessions_SessionId ON UserSessions(SessionId);
                CREATE INDEX IF NOT EXISTS IX_UserSessions_IsActive ON UserSessions(IsActive);");
        }

        /// <summary>
        /// تحديث هيكل جدول UserPermissions
        /// </summary>
        private void UpdateUserPermissionsTableStructure()
        {
            using var connection = GetConnection();
            connection.Open();

            // التحقق من وجود الجدول
            var tableExists = connection.QuerySingle<int>(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='UserPermissions'");

            if (tableExists == 0)
            {
                return; // الجدول غير موجود، سيتم إنشاؤه بالهيكل الصحيح
            }

            // قائمة الأعمدة المطلوب إضافتها
            var columnsToAdd = new Dictionary<string, string>
            {
                { "IsGranted", "BOOLEAN NOT NULL DEFAULT 1" },
                { "GrantedDate", "DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP" },
                { "ExpiryDate", "DATETIME" },
                { "Reason", "TEXT" },
                { "GrantedByUserId", "INTEGER" },
                { "Priority", "INTEGER NOT NULL DEFAULT 0" },
                { "Notes", "TEXT" },
                { "CreatedDate", "DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP" },
                { "UpdatedDate", "DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP" }
            };

            // التحقق من وجود كل عمود وإضافته إذا لم يكن موجوداً
            foreach (var column in columnsToAdd)
            {
                try
                {
                    var columnExists = connection.QuerySingle<int>(
                        $"SELECT COUNT(*) FROM pragma_table_info('UserPermissions') WHERE name='{column.Key}'");

                    if (columnExists == 0)
                    {
                        using var command = connection.CreateCommand();
                        command.CommandText = $"ALTER TABLE UserPermissions ADD COLUMN {column.Key} {column.Value}";
                        command.ExecuteNonQuery();
                    }
                }
                catch (SQLiteException ex)
                {
                    // تجاهل الخطأ إذا كان العمود موجوداً بالفعل
                    if (!ex.Message.Contains("duplicate column name"))
                    {
                        throw;
                    }
                }
            }

            // إضافة الفهارس إذا لم تكن موجودة
            try
            {
                connection.Execute(@"
                    CREATE INDEX IF NOT EXISTS IX_UserPermissions_GrantedByUserId ON UserPermissions(GrantedByUserId);
                    CREATE INDEX IF NOT EXISTS IX_UserPermissions_Priority ON UserPermissions(Priority);
                    CREATE INDEX IF NOT EXISTS IX_UserPermissions_IsActive ON UserPermissions(IsActive);");
            }
            catch (SQLiteException)
            {
                // تجاهل أخطاء الفهارس الموجودة
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع جدول بيانات
        /// </summary>
        public DataTable ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            DataTable dataTable = new DataTable();

            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = query;

                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value);
                        }
                    }

                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }

            return dataTable;
        }

        /// <summary>
        /// إنشاء البيانات الافتراضية للمنتجات
        /// </summary>
        public void CreateDefaultProductData()
        {
            using var connection = GetConnection();
            connection.Open();

            // إنشاء وحدات القياس الافتراضية
            var unitsCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM UnitsOfMeasure");
            if (unitsCount == 0)
            {
                var defaultUnits = new[]
                {
                    new { Name = "قطعة", Symbol = "قطعة", Description = "وحدة العد الأساسية" },
                    new { Name = "كيلوجرام", Symbol = "كجم", Description = "وحدة الوزن" },
                    new { Name = "جرام", Symbol = "جم", Description = "وحدة الوزن الصغيرة" },
                    new { Name = "لتر", Symbol = "لتر", Description = "وحدة الحجم" },
                    new { Name = "متر", Symbol = "م", Description = "وحدة الطول" },
                    new { Name = "سنتيمتر", Symbol = "سم", Description = "وحدة الطول الصغيرة" },
                    new { Name = "صندوق", Symbol = "صندوق", Description = "وحدة التعبئة" },
                    new { Name = "كرتونة", Symbol = "كرتونة", Description = "وحدة التعبئة الكبيرة" },
                    new { Name = "دزينة", Symbol = "دزينة", Description = "12 قطعة" },
                    new { Name = "طن", Symbol = "طن", Description = "1000 كيلوجرام" }
                };

                foreach (var unit in defaultUnits)
                {
                    connection.Execute(@"
                        INSERT INTO UnitsOfMeasure (Name, Symbol, Description, IsActive, CreatedDate)
                        VALUES (@Name, @Symbol, @Description, 1, @CreatedDate)",
                        new { unit.Name, unit.Symbol, unit.Description, CreatedDate = DateTime.Now });
                }
            }

            // إنشاء تصنيفات المنتجات الافتراضية
            var categoriesCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM ProductCategories");
            if (categoriesCount == 0)
            {
                var defaultCategories = new[]
                {
                    new { Name = "عام", Description = "تصنيف عام للمنتجات", Color = "#007ACC" },
                    new { Name = "إلكترونيات", Description = "الأجهزة الإلكترونية والكهربائية", Color = "#FF6B35" },
                    new { Name = "ملابس", Description = "الملابس والأزياء", Color = "#F7931E" },
                    new { Name = "طعام ومشروبات", Description = "المواد الغذائية والمشروبات", Color = "#8BC34A" },
                    new { Name = "مستحضرات تجميل", Description = "منتجات العناية والتجميل", Color = "#E91E63" },
                    new { Name = "أدوات منزلية", Description = "الأدوات والمعدات المنزلية", Color = "#9C27B0" },
                    new { Name = "كتب وقرطاسية", Description = "الكتب والأدوات المكتبية", Color = "#3F51B5" },
                    new { Name = "رياضة وترفيه", Description = "المعدات الرياضية وأدوات الترفيه", Color = "#009688" },
                    new { Name = "سيارات وقطع غيار", Description = "قطع غيار السيارات والإكسسوارات", Color = "#795548" },
                    new { Name = "صحة وعناية", Description = "المنتجات الصحية ومنتجات العناية", Color = "#4CAF50" }
                };

                foreach (var category in defaultCategories)
                {
                    connection.Execute(@"
                        INSERT INTO ProductCategories (Name, Description, Color, IsActive, CreatedDate)
                        VALUES (@Name, @Description, @Color, 1, @CreatedDate)",
                        new { category.Name, category.Description, category.Color, CreatedDate = DateTime.Now });
                }
            }
        }

        /// <summary>
        /// تحديث قاعدة البيانات لإضافة الأعمدة والجداول الجديدة
        /// </summary>
        public async Task UpdateDatabaseAsync()
        {
            try
            {
                var updater = new DatabaseUpdater();
                await updater.UpdateDatabaseAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث قاعدة البيانات: {ex.Message}");
                throw;
            }
        }
    }
}
