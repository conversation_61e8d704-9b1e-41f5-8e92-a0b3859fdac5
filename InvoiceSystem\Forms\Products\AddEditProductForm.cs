using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    public partial class AddEditProductForm : Form
    {
        private readonly ProductService _productService;
        private readonly ProductCategoryService _categoryService;
        private readonly UnitOfMeasureService _unitService;
        private readonly Product? _product;
        private readonly bool _isEditMode;
        private List<ProductCategory> _categories;
        private List<UnitOfMeasure> _units;

        public AddEditProductForm(Product? product = null)
        {
            InitializeComponent();
            _productService = new ProductService();
            _categoryService = new ProductCategoryService();
            _unitService = new UnitOfMeasureService();
            _product = product;
            _isEditMode = product != null;
            _categories = new List<ProductCategory>();
            _units = new List<UnitOfMeasure>();

            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            if (_isEditMode)
            {
                this.Text = "تعديل منتج";
            }
            else
            {
                this.Text = "إضافة منتج جديد";
            }

            LoadFormDataAsync();
        }

        private async void LoadFormDataAsync()
        {
            try
            {
                // تحميل التصنيفات
                _categories = await _categoryService.GetActiveCategoriesAsync();
                cmbCategory.DataSource = _categories;
                cmbCategory.DisplayMember = "Name";
                cmbCategory.ValueMember = "Id";
                cmbCategory.SelectedIndex = -1;

                // تحميل وحدات القياس
                _units = await _unitService.GetActiveUnitsAsync();
                cmbUnit.DataSource = _units;
                cmbUnit.DisplayMember = "DisplayName";
                cmbUnit.ValueMember = "Id";
                cmbUnit.SelectedIndex = -1;

                // تحميل بيانات المنتج إذا كان في وضع التعديل
                if (_isEditMode && _product != null)
                {
                    LoadProductData();
                }
                else
                {
                    SetDefaultValues();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductData()
        {
            if (_product != null)
            {
                txtName.Text = _product.Name;
                txtDescription.Text = _product.Description;
                numPrice.Value = _product.Price;
                numCost.Value = _product.Cost;
                numStock.Value = _product.Stock;
                numLowStockThreshold.Value = _product.LowStockThreshold;
                txtBarcode.Text = _product.Barcode ?? string.Empty;
                chkIsActive.Checked = _product.IsActive;

                // تحديد التصنيف
                if (_product.CategoryId.HasValue)
                {
                    cmbCategory.SelectedValue = _product.CategoryId.Value;
                }

                // تحديد وحدة القياس
                if (_product.UnitOfMeasureId.HasValue)
                {
                    cmbUnit.SelectedValue = _product.UnitOfMeasureId.Value;
                }

                UpdateProfitMargin();
            }
        }

        private void SetDefaultValues()
        {
            chkIsActive.Checked = true;
            numLowStockThreshold.Value = 10;
            numStock.Value = 0;
            numCost.Value = 0;
            numPrice.Value = 0;
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrWhiteSpace(txtBarcode.Text))
                {
                    var excludeId = _isEditMode ? _product?.Id : null;
                    var barcodeExists = await _productService.IsBarcodeExistsAsync(txtBarcode.Text.Trim(), excludeId);

                    if (barcodeExists)
                    {
                        MessageBox.Show("يوجد منتج آخر بنفس الباركود", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtBarcode.Focus();
                        return;
                    }
                }

                var product = new Product
                {
                    Name = txtName.Text.Trim(),
                    Description = txtDescription.Text.Trim(),
                    Price = numPrice.Value,
                    Cost = numCost.Value,
                    Stock = (int)numStock.Value,
                    LowStockThreshold = (int)numLowStockThreshold.Value,
                    Barcode = string.IsNullOrWhiteSpace(txtBarcode.Text) ? null : txtBarcode.Text.Trim(),
                    CategoryId = cmbCategory.SelectedValue as int?,
                    UnitOfMeasureId = cmbUnit.SelectedValue as int?,
                    IsActive = chkIsActive.Checked
                };

                if (_isEditMode && _product != null)
                {
                    product.Id = _product.Id;
                    await _productService.UpdateProductAsync(product);
                    MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _productService.AddProductAsync(product);
                    MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (numPrice.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numPrice.Focus();
                return false;
            }

            if (numStock.Value < 0)
            {
                MessageBox.Show("لا يمكن أن تكون الكمية سالبة", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numStock.Focus();
                return false;
            }

            if (txtName.Text.Length > 200)
            {
                MessageBox.Show("اسم المنتج يجب أن يكون أقل من 200 حرف", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtBarcode.Text) && txtBarcode.Text.Length > 100)
            {
                MessageBox.Show("الباركود يجب أن يكون أقل من 100 حرف", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtBarcode.Focus();
                return false;
            }

            return true;
        }

        private void UpdateProfitMargin()
        {
            if (numPrice.Value > 0 && numCost.Value >= 0)
            {
                var margin = ((numPrice.Value - numCost.Value) / numPrice.Value) * 100;
                lblProfitMargin.Text = $"هامش الربح: {margin:F2}%";
                lblProfitMargin.ForeColor = margin >= 0 ? Color.Green : Color.Red;
            }
            else
            {
                lblProfitMargin.Text = "هامش الربح: --";
                lblProfitMargin.ForeColor = Color.Black;
            }
        }

        private void UpdateStockStatus()
        {
            var stock = (int)numStock.Value;
            var threshold = (int)numLowStockThreshold.Value;

            if (stock <= 0)
            {
                lblStockStatus.Text = "حالة المخزون: نفد المخزون";
                lblStockStatus.ForeColor = Color.Red;
            }
            else if (stock <= threshold)
            {
                lblStockStatus.Text = "حالة المخزون: مخزون منخفض";
                lblStockStatus.ForeColor = Color.Orange;
            }
            else
            {
                lblStockStatus.Text = "حالة المخزون: متوفر";
                lblStockStatus.ForeColor = Color.Green;
            }
        }

        private void numPrice_ValueChanged(object sender, EventArgs e)
        {
            UpdateProfitMargin();
        }

        private void numCost_ValueChanged(object sender, EventArgs e)
        {
            UpdateProfitMargin();
        }

        private void numStock_ValueChanged(object sender, EventArgs e)
        {
            UpdateStockStatus();
        }

        private void numLowStockThreshold_ValueChanged(object sender, EventArgs e)
        {
            UpdateStockStatus();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
