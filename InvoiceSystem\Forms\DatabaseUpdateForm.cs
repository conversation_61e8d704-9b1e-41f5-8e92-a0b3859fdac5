using InvoiceSystem.Data;
using InvoiceSystem.Database;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms
{
    /// <summary>
    /// نافذة تحديث قاعدة البيانات
    /// </summary>
    public partial class DatabaseUpdateForm : Form
    {
        private readonly DatabaseHelper _dbHelper;
        private readonly DatabaseUpdater _updater;

        public DatabaseUpdateForm()
        {
            InitializeComponent();
            _dbHelper = new DatabaseHelper();
            _updater = new DatabaseUpdater();
            
            this.Text = "تحديث قاعدة البيانات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                btnUpdate.Enabled = false;
                btnClose.Enabled = false;
                progressBar.Visible = true;
                lblStatus.Text = "جاري تحديث قاعدة البيانات...";
                lblStatus.ForeColor = Color.Blue;

                progressBar.Value = 10;
                await Task.Delay(500);

                // تحديث قاعدة البيانات
                await _updater.UpdateDatabaseAsync();
                
                progressBar.Value = 100;
                lblStatus.Text = "تم تحديث قاعدة البيانات بنجاح!";
                lblStatus.ForeColor = Color.Green;

                MessageBox.Show("تم تحديث قاعدة البيانات بنجاح!\n\nتم إضافة الأعمدة والجداول الجديدة.", 
                    "نجح التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                lblStatus.Text = "فشل في تحديث قاعدة البيانات";
                lblStatus.ForeColor = Color.Red;
                
                MessageBox.Show($"خطأ في تحديث قاعدة البيانات:\n\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnUpdate.Enabled = true;
                btnClose.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void DatabaseUpdateForm_Load(object sender, EventArgs e)
        {
            lblInfo.Text = @"سيتم تحديث قاعدة البيانات لإضافة الميزات الجديدة:

• إضافة عمود IsActive للمنتجات
• إضافة عمود Cost (التكلفة)
• إضافة عمود LowStockThreshold (حد المخزون المنخفض)
• إضافة عمود Barcode (الباركود)
• إضافة عمود CategoryId (معرف التصنيف)
• إضافة عمود UnitOfMeasureId (معرف وحدة القياس)
• إنشاء جدول تصنيفات المنتجات
• إنشاء جدول وحدات القياس
• إنشاء جدول حركات المنتجات
• إدراج البيانات الافتراضية

هذا التحديث آمن ولن يؤثر على البيانات الموجودة.";
        }
    }
}
