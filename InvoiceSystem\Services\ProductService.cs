using Dapper;
using InvoiceSystem.Data;
using InvoiceSystem.Models;

namespace InvoiceSystem.Services
{
    public class ProductService
    {
        private readonly DatabaseHelper _dbHelper;

        public ProductService()
        {
            _dbHelper = new DatabaseHelper();
        }

        public ProductService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        public async Task<List<Product>> GetAllProductsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1
                  ORDER BY p.Name",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                splitOn: "Id,Id");
            return products.ToList();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.Id = @Id",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                new { Id = id },
                splitOn: "Id,Id");
            return products.FirstOrDefault();
        }

        public async Task<int> AddProductAsync(Product product)
        {
            using var connection = _dbHelper.GetConnection();
            product.CreatedDate = DateTime.Now;

            var sql = @"INSERT INTO Products (Name, Description, Price, Cost, Stock, LowStockThreshold,
                                             Barcode, CategoryId, UnitOfMeasureId, IsActive, CreatedDate)
                       VALUES (@Name, @Description, @Price, @Cost, @Stock, @LowStockThreshold,
                               @Barcode, @CategoryId, @UnitOfMeasureId, @IsActive, @CreatedDate);
                       SELECT last_insert_rowid();";

            return await connection.QuerySingleAsync<int>(sql, product);
        }

        public async Task<bool> UpdateProductAsync(Product product)
        {
            using var connection = _dbHelper.GetConnection();
            product.UpdatedDate = DateTime.Now;

            var sql = @"UPDATE Products
                       SET Name = @Name, Description = @Description, Price = @Price, Cost = @Cost,
                           Stock = @Stock, LowStockThreshold = @LowStockThreshold, Barcode = @Barcode,
                           CategoryId = @CategoryId, UnitOfMeasureId = @UnitOfMeasureId,
                           IsActive = @IsActive, UpdatedDate = @UpdatedDate
                       WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, product);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync("DELETE FROM Products WHERE Id = @Id", new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateStockAsync(int productId, int newStock)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Products SET Stock = @Stock WHERE Id = @Id",
                new { Stock = newStock, Id = productId });
            return rowsAffected > 0;
        }

        public async Task<bool> ReduceStockAsync(int productId, int quantity)
        {
            using var connection = _dbHelper.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Products SET Stock = Stock - @Quantity WHERE Id = @Id AND Stock >= @Quantity",
                new { Quantity = quantity, Id = productId });
            return rowsAffected > 0;
        }

        public async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1 AND (p.Name LIKE @SearchTerm OR p.Description LIKE @SearchTerm OR p.Barcode LIKE @SearchTerm)
                  ORDER BY p.Name",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                new { SearchTerm = $"%{searchTerm}%" },
                splitOn: "Id,Id");
            return products.ToList();
        }

        /// <summary>
        /// الحصول على المنتجات ذات المخزون المنخفض
        /// </summary>
        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1 AND p.Stock <= p.LowStockThreshold AND p.Stock > 0
                  ORDER BY p.Stock ASC",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                splitOn: "Id,Id");
            return products.ToList();
        }

        /// <summary>
        /// الحصول على المنتجات النافدة من المخزون
        /// </summary>
        public async Task<List<Product>> GetOutOfStockProductsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1 AND p.Stock <= 0
                  ORDER BY p.Name",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                splitOn: "Id,Id");
            return products.ToList();
        }

        /// <summary>
        /// الحصول على المنتجات حسب التصنيف
        /// </summary>
        public async Task<List<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1 AND p.CategoryId = @CategoryId
                  ORDER BY p.Name",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                new { CategoryId = categoryId },
                splitOn: "Id,Id");
            return products.ToList();
        }

        public async Task<List<Product>> GetLowStockProductsAsync(int threshold = 10)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product>(
                "SELECT * FROM Products WHERE Stock <= @Threshold ORDER BY Stock",
                new { Threshold = threshold });
            return products.ToList();
        }

        /// <summary>
        /// الحصول على إحصائيات المنتجات
        /// </summary>
        public async Task<Dictionary<string, object>> GetProductStatisticsAsync()
        {
            using var connection = _dbHelper.GetConnection();

            var totalProducts = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE IsActive = 1");

            var outOfStock = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND Stock <= 0");

            var lowStock = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND Stock > 0 AND Stock <= LowStockThreshold");

            var inStock = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND Stock > LowStockThreshold");

            var totalValue = await connection.QuerySingleAsync<decimal>(
                "SELECT COALESCE(SUM(Price * Stock), 0) FROM Products WHERE IsActive = 1");

            var totalCost = await connection.QuerySingleAsync<decimal>(
                "SELECT COALESCE(SUM(Cost * Stock), 0) FROM Products WHERE IsActive = 1");

            return new Dictionary<string, object>
            {
                { "TotalProducts", totalProducts },
                { "OutOfStock", outOfStock },
                { "LowStock", lowStock },
                { "InStock", inStock },
                { "TotalValue", totalValue },
                { "TotalCost", totalCost },
                { "PotentialProfit", totalValue - totalCost }
            };
        }

        /// <summary>
        /// البحث بالباركود
        /// </summary>
        public async Task<Product?> GetProductByBarcodeAsync(string barcode)
        {
            using var connection = _dbHelper.GetConnection();
            var products = await connection.QueryAsync<Product, ProductCategory, UnitOfMeasure, Product>(
                @"SELECT p.*, c.*, u.*
                  FROM Products p
                  LEFT JOIN ProductCategories c ON p.CategoryId = c.Id
                  LEFT JOIN UnitsOfMeasure u ON p.UnitOfMeasureId = u.Id
                  WHERE p.IsActive = 1 AND p.Barcode = @Barcode",
                (product, category, unit) =>
                {
                    product.Category = category;
                    product.UnitOfMeasure = unit;
                    return product;
                },
                new { Barcode = barcode },
                splitOn: "Id,Id");
            return products.FirstOrDefault();
        }

        /// <summary>
        /// التحقق من وجود باركود مكرر
        /// </summary>
        public async Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM Products WHERE Barcode = @Barcode AND IsActive = 1";
            var parameters = new { Barcode = barcode };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                parameters = new { Barcode = barcode, ExcludeId = excludeId.Value };
            }

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count > 0;
        }

        /// <summary>
        /// تسجيل حركة مبيعات منتج
        /// </summary>
        public async Task<bool> RecordSaleAsync(int productId, int quantity, decimal unitPrice, string? reference = null)
        {
            var movementService = new ProductMovementService(_dbHelper);
            var movement = new ProductMovement
            {
                ProductId = productId,
                Type = ProductMovementType.Sale,
                Quantity = quantity,
                UnitPrice = unitPrice,
                Reference = reference
            };

            var movementId = await movementService.AddMovementAsync(movement);
            return movementId > 0;
        }

        /// <summary>
        /// تسجيل حركة مردود مبيعات منتج
        /// </summary>
        public async Task<bool> RecordSaleReturnAsync(int productId, int quantity, decimal unitPrice, string? reference = null)
        {
            var movementService = new ProductMovementService(_dbHelper);
            var movement = new ProductMovement
            {
                ProductId = productId,
                Type = ProductMovementType.SaleReturn,
                Quantity = quantity,
                UnitPrice = unitPrice,
                Reference = reference
            };

            var movementId = await movementService.AddMovementAsync(movement);
            return movementId > 0;
        }
    }
}
