using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    /// <summary>
    /// نافذة إضافة/تعديل وحدة القياس
    /// </summary>
    public partial class AddEditUnitOfMeasureForm : Form
    {
        private readonly UnitOfMeasureService _unitService;
        private readonly UnitOfMeasure? _unit;
        private readonly bool _isEditMode;

        public AddEditUnitOfMeasureForm(UnitOfMeasure? unit = null)
        {
            InitializeComponent();
            _unitService = new UnitOfMeasureService();
            _unit = unit;
            _isEditMode = unit != null;

            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            if (_isEditMode)
            {
                this.Text = "تعديل وحدة القياس";
                LoadUnitData();
            }
            else
            {
                this.Text = "إضافة وحدة قياس جديدة";
                SetDefaultValues();
            }
        }

        private void LoadUnitData()
        {
            if (_unit != null)
            {
                txtName.Text = _unit.Name;
                txtSymbol.Text = _unit.Symbol;
                txtDescription.Text = _unit.Description;
                chkIsActive.Checked = _unit.IsActive;
            }
        }

        private void SetDefaultValues()
        {
            chkIsActive.Checked = true;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم وحدة القياس", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtName.Text.Length > 50)
            {
                MessageBox.Show("اسم وحدة القياس يجب أن يكون أقل من 50 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtSymbol.Text.Length > 10)
            {
                MessageBox.Show("رمز وحدة القياس يجب أن يكون أقل من 10 أحرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSymbol.Focus();
                return false;
            }

            if (txtDescription.Text.Length > 200)
            {
                MessageBox.Show("الوصف يجب أن يكون أقل من 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // التحقق من عدم تكرار الاسم
                var excludeId = _isEditMode ? _unit?.Id : null;
                var nameExists = await _unitService.IsUnitNameExistsAsync(txtName.Text.Trim(), excludeId);

                if (nameExists)
                {
                    MessageBox.Show("يوجد وحدة قياس أخرى بنفس الاسم", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // التحقق من عدم تكرار الرمز
                if (!string.IsNullOrWhiteSpace(txtSymbol.Text))
                {
                    var symbolExists = await _unitService.IsUnitSymbolExistsAsync(txtSymbol.Text.Trim(), excludeId);

                    if (symbolExists)
                    {
                        MessageBox.Show("يوجد وحدة قياس أخرى بنفس الرمز", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtSymbol.Focus();
                        return;
                    }
                }

                var unit = new UnitOfMeasure
                {
                    Name = txtName.Text.Trim(),
                    Symbol = txtSymbol.Text.Trim(),
                    Description = txtDescription.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                if (_isEditMode && _unit != null)
                {
                    unit.Id = _unit.Id;
                    await _unitService.UpdateUnitAsync(unit);
                    MessageBox.Show("تم تحديث وحدة القياس بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitService.AddUnitAsync(unit);
                    MessageBox.Show("تم إضافة وحدة القياس بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ وحدة القياس: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtName_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الاسم
            lblPreviewName.Text = string.IsNullOrWhiteSpace(txtName.Text) ? "اسم الوحدة" : txtName.Text;
            UpdatePreviewDisplay();
        }

        private void txtSymbol_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الرمز
            lblPreviewSymbol.Text = string.IsNullOrWhiteSpace(txtSymbol.Text) ? "الرمز" : txtSymbol.Text;
            UpdatePreviewDisplay();
        }

        private void UpdatePreviewDisplay()
        {
            var name = string.IsNullOrWhiteSpace(txtName.Text) ? "اسم الوحدة" : txtName.Text;
            var symbol = string.IsNullOrWhiteSpace(txtSymbol.Text) ? "" : $" ({txtSymbol.Text})";
            lblPreviewDisplay.Text = $"{name}{symbol}";
        }
    }
}
