using InvoiceSystem.Models;
using InvoiceSystem.Services;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InvoiceSystem.Forms.Products
{
    /// <summary>
    /// نافذة إضافة/تعديل وحدة القياس
    /// </summary>
    public partial class AddEditUnitOfMeasureForm : Form
    {
        private readonly UnitOfMeasureService _unitService;
        private readonly UnitOfMeasure? _unit;
        private readonly bool _isEditMode;

        public AddEditUnitOfMeasureForm(UnitOfMeasure? unit = null)
        {
            InitializeComponent();
            _unitService = new UnitOfMeasureService();
            _unit = unit;
            _isEditMode = unit != null;

            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            if (_isEditMode)
            {
                this.Text = "تعديل وحدة القياس";
                LoadUnitData();
            }
            else
            {
                this.Text = "إضافة وحدة قياس جديدة";
                SetDefaultValues();
            }
        }

        private void LoadUnitData()
        {
            if (_unit != null)
            {
                txtName.Text = _unit.Name;
                txtSymbol.Text = _unit.Symbol;
                txtDescription.Text = _unit.Description;
                chkIsActive.Checked = _unit.IsActive;
            }
        }

        private void SetDefaultValues()
        {
            chkIsActive.Checked = true;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم وحدة القياس", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtName.Text.Length > 50)
            {
                MessageBox.Show("اسم وحدة القياس يجب أن يكون أقل من 50 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (txtSymbol.Text.Length > 10)
            {
                MessageBox.Show("رمز وحدة القياس يجب أن يكون أقل من 10 أحرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSymbol.Focus();
                return false;
            }

            if (txtDescription.Text.Length > 200)
            {
                MessageBox.Show("الوصف يجب أن يكون أقل من 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // التحقق من عدم تكرار الاسم
                var excludeId = _isEditMode ? _unit?.Id : null;
                var nameExists = await _unitService.IsUnitNameExistsAsync(txtName.Text.Trim(), excludeId);

                if (nameExists)
                {
                    MessageBox.Show("يوجد وحدة قياس أخرى بنفس الاسم", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // التحقق من عدم تكرار الرمز
                if (!string.IsNullOrWhiteSpace(txtSymbol.Text))
                {
                    var symbolExists = await _unitService.IsUnitSymbolExistsAsync(txtSymbol.Text.Trim(), excludeId);

                    if (symbolExists)
                    {
                        MessageBox.Show("يوجد وحدة قياس أخرى بنفس الرمز", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtSymbol.Focus();
                        return;
                    }
                }

                var unit = new UnitOfMeasure
                {
                    Name = txtName.Text.Trim(),
                    Symbol = txtSymbol.Text.Trim(),
                    Description = txtDescription.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                if (_isEditMode && _unit != null)
                {
                    unit.Id = _unit.Id;
                    await _unitService.UpdateUnitAsync(unit);
                    MessageBox.Show("تم تحديث وحدة القياس بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitService.AddUnitAsync(unit);
                    MessageBox.Show("تم إضافة وحدة القياس بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ وحدة القياس: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtName_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الاسم
            lblPreviewName.Text = string.IsNullOrWhiteSpace(txtName.Text) ? "اسم الوحدة" : txtName.Text;
        }

        private void txtSymbol_TextChanged(object sender, EventArgs e)
        {
            // تحديث معاينة الرمز
            lblPreviewSymbol.Text = string.IsNullOrWhiteSpace(txtSymbol.Text) ? "الرمز" : txtSymbol.Text;
            UpdatePreviewDisplay();
        }

        private void UpdatePreviewDisplay()
        {
            var name = string.IsNullOrWhiteSpace(txtName.Text) ? "اسم الوحدة" : txtName.Text;
            var symbol = string.IsNullOrWhiteSpace(txtSymbol.Text) ? "" : $" ({txtSymbol.Text})";
            lblPreviewDisplay.Text = $"{name}{symbol}";
        }
    }

    partial class AddEditUnitOfMeasureForm
    {
        private System.ComponentModel.IContainer components = null;
        private TextBox txtName;
        private TextBox txtSymbol;
        private TextBox txtDescription;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Label lblName;
        private Label lblSymbol;
        private Label lblDescription;
        private GroupBox groupBoxPreview;
        private Label lblPreviewName;
        private Label lblPreviewSymbol;
        private Label lblPreviewDisplay;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.txtName = new TextBox();
            this.txtSymbol = new TextBox();
            this.txtDescription = new TextBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblName = new Label();
            this.lblSymbol = new Label();
            this.lblDescription = new Label();
            this.groupBoxPreview = new GroupBox();
            this.lblPreviewName = new Label();
            this.lblPreviewSymbol = new Label();
            this.lblPreviewDisplay = new Label();

            this.groupBoxPreview.SuspendLayout();
            this.SuspendLayout();

            //
            // lblName
            //
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(12, 15);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(70, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "اسم الوحدة:";

            //
            // txtName
            //
            this.txtName.Location = new Point(12, 35);
            this.txtName.MaxLength = 50;
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(200, 23);
            this.txtName.TabIndex = 1;
            this.txtName.TextChanged += new EventHandler(this.txtName_TextChanged);

            //
            // lblSymbol
            //
            this.lblSymbol.AutoSize = true;
            this.lblSymbol.Location = new Point(230, 15);
            this.lblSymbol.Name = "lblSymbol";
            this.lblSymbol.Size = new Size(35, 15);
            this.lblSymbol.TabIndex = 2;
            this.lblSymbol.Text = "الرمز:";

            //
            // txtSymbol
            //
            this.txtSymbol.Location = new Point(230, 35);
            this.txtSymbol.MaxLength = 10;
            this.txtSymbol.Name = "txtSymbol";
            this.txtSymbol.Size = new Size(80, 23);
            this.txtSymbol.TabIndex = 3;
            this.txtSymbol.TextChanged += new EventHandler(this.txtSymbol_TextChanged);

            //
            // lblDescription
            //
            this.lblDescription.AutoSize = true;
            this.lblDescription.Location = new Point(12, 70);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new Size(38, 15);
            this.lblDescription.TabIndex = 4;
            this.lblDescription.Text = "الوصف:";

            //
            // txtDescription
            //
            this.txtDescription.Location = new Point(12, 90);
            this.txtDescription.MaxLength = 200;
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.ScrollBars = ScrollBars.Vertical;
            this.txtDescription.Size = new Size(298, 60);
            this.txtDescription.TabIndex = 5;

            //
            // chkIsActive
            //
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Checked = true;
            this.chkIsActive.CheckState = CheckState.Checked;
            this.chkIsActive.Location = new Point(12, 165);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Size = new Size(48, 19);
            this.chkIsActive.TabIndex = 6;
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.UseVisualStyleBackColor = true;

            //
            // groupBoxPreview
            //
            this.groupBoxPreview.Controls.Add(this.lblPreviewName);
            this.groupBoxPreview.Controls.Add(this.lblPreviewSymbol);
            this.groupBoxPreview.Controls.Add(this.lblPreviewDisplay);
            this.groupBoxPreview.Location = new Point(330, 35);
            this.groupBoxPreview.Name = "groupBoxPreview";
            this.groupBoxPreview.Size = new Size(200, 115);
            this.groupBoxPreview.TabIndex = 7;
            this.groupBoxPreview.TabStop = false;
            this.groupBoxPreview.Text = "معاينة";

            //
            // lblPreviewName
            //
            this.lblPreviewName.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPreviewName.Location = new Point(10, 20);
            this.lblPreviewName.Name = "lblPreviewName";
            this.lblPreviewName.Size = new Size(180, 25);
            this.lblPreviewName.TabIndex = 0;
            this.lblPreviewName.Text = "اسم الوحدة";

            //
            // lblPreviewSymbol
            //
            this.lblPreviewSymbol.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            this.lblPreviewSymbol.ForeColor = Color.Gray;
            this.lblPreviewSymbol.Location = new Point(10, 45);
            this.lblPreviewSymbol.Name = "lblPreviewSymbol";
            this.lblPreviewSymbol.Size = new Size(180, 20);
            this.lblPreviewSymbol.TabIndex = 1;
            this.lblPreviewSymbol.Text = "الرمز";

            //
            // lblPreviewDisplay
            //
            this.lblPreviewDisplay.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblPreviewDisplay.ForeColor = Color.Blue;
            this.lblPreviewDisplay.Location = new Point(10, 70);
            this.lblPreviewDisplay.Name = "lblPreviewDisplay";
            this.lblPreviewDisplay.Size = new Size(180, 35);
            this.lblPreviewDisplay.TabIndex = 2;
            this.lblPreviewDisplay.Text = "اسم الوحدة (الرمز)";

            //
            // btnSave
            //
            this.btnSave.BackColor = Color.FromArgb(46, 204, 113);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(350, 165);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(90, 35);
            this.btnSave.TabIndex = 8;
            this.btnSave.Text = "💾 حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            //
            // btnCancel
            //
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(450, 165);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(90, 35);
            this.btnCancel.TabIndex = 9;
            this.btnCancel.Text = "❌ إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            //
            // AddEditUnitOfMeasureForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(550, 220);
            this.Controls.Add(this.lblName);
            this.Controls.Add(this.txtName);
            this.Controls.Add(this.lblSymbol);
            this.Controls.Add(this.txtSymbol);
            this.Controls.Add(this.lblDescription);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.groupBoxPreview);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditUnitOfMeasureForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة وحدة قياس جديدة";

            this.groupBoxPreview.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
