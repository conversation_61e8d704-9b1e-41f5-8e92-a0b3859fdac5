-- تحديث جدول المنتجات لإضافة الأعمدة الجديدة
-- Update Products table to add new columns

-- إضافة عمود IsActive إذا لم يكن موجوداً
-- Add IsActive column if it doesn't exist
ALTER TABLE Products ADD COLUMN IsActive INTEGER DEFAULT 1;

-- إضافة عمود Cost إذا لم يكن موجوداً
-- Add Cost column if it doesn't exist
ALTER TABLE Products ADD COLUMN Cost DECIMAL(10,2) DEFAULT 0;

-- إضافة عمود LowStockThreshold إذا لم يكن موجوداً
-- Add LowStockThreshold column if it doesn't exist
ALTER TABLE Products ADD COLUMN LowStockThreshold INTEGER DEFAULT 10;

-- إضافة عمود Barcode إذا لم يكن موجوداً
-- Add Barcode column if it doesn't exist
ALTER TABLE Products ADD COLUMN Barcode TEXT;

-- إضافة عمود CategoryId إذا لم يكن موجوداً
-- Add CategoryId column if it doesn't exist
ALTER TABLE Products ADD COLUMN CategoryId INTEGER;

-- إضا<PERSON>ة عمود UnitOfMeasureId إذا لم يكن موجوداً
-- Add UnitOfMeasureId column if it doesn't exist
ALTER TABLE Products ADD COLUMN UnitOfMeasureId INTEGER;

-- إضافة عمود UpdatedDate إذا لم يكن موجوداً
-- Add UpdatedDate column if it doesn't exist
ALTER TABLE Products ADD COLUMN UpdatedDate DATETIME;

-- تحديث القيم الافتراضية للمنتجات الموجودة
-- Update default values for existing products
UPDATE Products 
SET IsActive = 1, 
    Cost = 0, 
    LowStockThreshold = 10,
    UpdatedDate = datetime('now')
WHERE IsActive IS NULL OR Cost IS NULL OR LowStockThreshold IS NULL;

-- إنشاء فهرس على عمود IsActive لتحسين الأداء
-- Create index on IsActive column for better performance
CREATE INDEX IF NOT EXISTS idx_products_isactive ON Products(IsActive);

-- إنشاء فهرس على عمود Barcode لتحسين الأداء
-- Create index on Barcode column for better performance
CREATE INDEX IF NOT EXISTS idx_products_barcode ON Products(Barcode);

-- إنشاء فهرس على عمود CategoryId لتحسين الأداء
-- Create index on CategoryId column for better performance
CREATE INDEX IF NOT EXISTS idx_products_categoryid ON Products(CategoryId);

-- إنشاء فهرس على عمود UnitOfMeasureId لتحسين الأداء
-- Create index on UnitOfMeasureId column for better performance
CREATE INDEX IF NOT EXISTS idx_products_unitofmeasureid ON Products(UnitOfMeasureId);
