{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Blackbox\\Invoices\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 154, "SelectedChildIndex": 7, "Children": [{"$type": "Bookmark", "Name": "ST:2:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:1966167525:0:{bf84ea66-d821-4dba-b1b1-2777d8574775}"}, {"$type": "Bookmark", "Name": "ST:1:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:0:0:{a958a09d-0df3-46ec-b584-ff0469ce7631}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:254354193:0:{71f361cc-493f-47c0-923f-f2570b6f8618}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Product.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Product.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Product.cs", "ViewState": "AgIAAGUAAAAAAAAAAAA0wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:31:10.246Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AddEditProductCategoryForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:29:17.571Z", "EditorCaption": " [Design]"}]}, {"DockedWidth": 176, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}]}]}]}