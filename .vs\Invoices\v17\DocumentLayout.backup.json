{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Blackbox\\Invoices\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\addchildaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\addchildaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\invoice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\invoice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\payment.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\payment.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\vouchermodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\vouchermodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\voucher.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\voucher.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\partner.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\partner.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\data\\customorm\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\data\\customorm\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\accounting\\simplifiedchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\simplifiedchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\journalentry.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\journalentry.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\invoiceitem.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\invoiceitem.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\customer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\customer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\comboboxitem.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\comboboxitem.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\auditlog.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\auditlog.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\accountingreports.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\accountingreports.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\models\\account.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\models\\account.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\newsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\newsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\controls\\newstickercontrol.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\controls\\newstickercontrol.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\news\\newsmanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\news\\newsmanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\notificationtestservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\notificationtestservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\enhancednotificationmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\enhancednotificationmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\popupnotificationform.designer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\popupnotificationform.designer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\enhancednotificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\enhancednotificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\enhancednotificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\enhancednotificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\notificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\notificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\notificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\notificationsettingsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\notificationsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\notificationsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\popupnotificationform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\popupnotificationform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\notificationdetailsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\notificationdetailsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\notifications\\popupnotificationform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\notifications\\popupnotificationform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\testing\\testdirectormform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\testing\\testdirectormform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\testing\\testdirectormform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\testing\\testdirectormform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\security\\addeditpermissionform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\security\\addeditpermissionform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\data\\directorm\\idbconnection.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\data\\directorm\\idbconnection.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\data\\directorm\\directdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\data\\directorm\\directdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\simplifiedchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\simplifiedchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\simplechartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\simplechartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\securemainform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\securemainform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\addeditaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\addeditaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\data\\databasehelper.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\data\\databasehelper.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\accounting\\addeditaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\addeditaccountform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\modernchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\modernchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\incomestatementreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\incomestatementreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\hierarchicalchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\hierarchicalchartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\generalledgerreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\generalledgerreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\cashflowreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\cashflowreportform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|C:\\Users\\<USER>\\Blackbox\\Invoices\\invoicesystem\\forms\\accounting\\chartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\accounting\\chartofaccountsform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 154, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 41, "Title": "SimplifiedChartOfAccountsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:38.782Z", "IsPinned": true, "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 12, "Title": "SimplifiedChartOfAccountsForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\SimplifiedChartOfAccountsForm.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAKkAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:08:45.78Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "DatabaseHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DatabaseHelper.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Data\\DatabaseHelper.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DatabaseHelper.cs", "RelativeToolTip": "InvoiceSystem\\Data\\DatabaseHelper.cs", "ViewState": "AgIAAHIAAAAAAAAAAAAswIkAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T15:08:54.769Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 3, "Title": "AddChildAccountForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddChildAccountForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\AddChildAccountForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddChildAccountForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\AddChildAccountForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T15:23:34.436Z", "IsPinned": true, "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "AddEditProductForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T21:47:05.201Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProductsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T20:37:06.204Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ProductService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\ProductService.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\ProductService.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\ProductService.cs", "RelativeToolTip": "InvoiceSystem\\Services\\ProductService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T20:23:13.991Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Invoice.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Invoice.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Invoice.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Invoice.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Invoice.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBIAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:57:44.995Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Payment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Payment.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Payment.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Payment.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Payment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:44:35.594Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "VoucherModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\VoucherModel.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\VoucherModel.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\VoucherModel.cs", "RelativeToolTip": "InvoiceSystem\\Models\\VoucherModel.cs", "ViewState": "AgIAABwAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:44:25.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Voucher.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Voucher.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Voucher.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Voucher.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Voucher.cs", "ViewState": "AgIAAAwAAAAAAAAAAAA4wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:44:17.014Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Product.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Product.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Product.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:42:52.903Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "BaseEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\CustomORM\\BaseEntity.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Data\\CustomORM\\BaseEntity.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\CustomORM\\BaseEntity.cs", "RelativeToolTip": "InvoiceSystem\\Data\\CustomORM\\BaseEntity.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAxwAgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:36:30.814Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Partner.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Partner.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Partner.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Partner.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Partner.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T19:35:26.178Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "JournalEntry.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\JournalEntry.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\JournalEntry.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\JournalEntry.cs", "RelativeToolTip": "InvoiceSystem\\Models\\JournalEntry.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIIAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T17:47:16.938Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "InvoiceItem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\InvoiceItem.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\InvoiceItem.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\InvoiceItem.cs", "RelativeToolTip": "InvoiceSystem\\Models\\InvoiceItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T17:47:12.808Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\User.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\User.cs", "RelativeToolTip": "InvoiceSystem\\Models\\User.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAswG4AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:39:40.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "Customer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Customer.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Customer.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Customer.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Customer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T17:47:05.699Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ComboBoxItem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\ComboBoxItem.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\ComboBoxItem.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\ComboBoxItem.cs", "RelativeToolTip": "InvoiceSystem\\Models\\ComboBoxItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T17:47:02.433Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:2:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:1966167525:0:{bf84ea66-d821-4dba-b1b1-2777d8574775}"}, {"$type": "Bookmark", "Name": "ST:1:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:0:0:{a958a09d-0df3-46ec-b584-ff0469ce7631}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:254354193:0:{71f361cc-493f-47c0-923f-f2570b6f8618}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 20, "Title": "NewsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\NewsService.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\NewsService.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\NewsService.cs", "RelativeToolTip": "InvoiceSystem\\Services\\NewsService.cs", "ViewState": "AgIAADEAAAAAAAAAAAAjwEoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T23:47:46.193Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "Account.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Account.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\Account.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\Account.cs", "RelativeToolTip": "InvoiceSystem\\Models\\Account.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:38:46.483Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "AccountingReports.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\AccountingReports.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\AccountingReports.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\AccountingReports.cs", "RelativeToolTip": "InvoiceSystem\\Models\\AccountingReports.cs", "ViewState": "AgIAACIAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:38:55.785Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "AuditLog.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\AuditLog.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Models\\AuditLog.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Models\\AuditLog.cs", "RelativeToolTip": "InvoiceSystem\\Models\\AuditLog.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:39:08.622Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "NewsManagementForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\News\\NewsManagementForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\News\\NewsManagementForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\News\\NewsManagementForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\News\\NewsManagementForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T23:47:39.409Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Program.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Program.cs", "RelativeToolTip": "InvoiceSystem\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T23:48:10.286Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "NewsTickerControl.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Controls\\NewsTickerControl.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Controls\\NewsTickerControl.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Controls\\NewsTickerControl.cs", "RelativeToolTip": "InvoiceSystem\\Controls\\NewsTickerControl.cs", "ViewState": "AgIAACYAAAAAAAAAAIA8wEcAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T00:09:16.858Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "NotificationTestService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\NotificationTestService.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\NotificationTestService.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\NotificationTestService.cs", "RelativeToolTip": "InvoiceSystem\\Services\\NotificationTestService.cs", "ViewState": "AgIAAE8BAAAAAAAAAAAjwF0BAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:46:00.28Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "EnhancedNotificationManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\EnhancedNotificationManager.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\EnhancedNotificationManager.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\EnhancedNotificationManager.cs", "RelativeToolTip": "InvoiceSystem\\Services\\EnhancedNotificationManager.cs", "ViewState": "AgIAACkBAAAAAAAAAAD4vzQBAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:45:45.444Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "PopupNotificationForm.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.Designer.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.Designer.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:43:50.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "AppShell.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs", "RelativeDocumentMoniker": "InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs", "RelativeToolTip": "InvoiceSystem\\InvoiceMauiApp\\AppShell.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:43:14.779Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "EnhancedNotificationSettingsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:36:21.335Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 28, "Title": "EnhancedNotificationSettingsForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\EnhancedNotificationSettingsForm.cs", "ViewState": "AgIAAN0AAAAAAAAAAAAIwAcAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:33:37.399Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "NotificationSettingsForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "ViewState": "AgIAACoBAAAAAAAAAAAYwDYBAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:32:25.66Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "NotificationSettingsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\NotificationSettingsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:33:42.216Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 32, "Title": "NotificationsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\NotificationsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\NotificationsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:33:46.528Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 33, "Title": "PopupNotificationForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:36:37.842Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 34, "Title": "NotificationDetailsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationDetailsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\NotificationDetailsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\NotificationDetailsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\NotificationDetailsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:33:38.491Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 35, "Title": "PopupNotificationForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Notifications\\PopupNotificationForm.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T20:33:49.169Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "AddEditPermissionForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Security\\AddEditPermissionForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Security\\AddEditPermissionForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Security\\AddEditPermissionForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Security\\AddEditPermissionForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:02:13.582Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "TestDirectORMForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T04:58:10.574Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 37, "Title": "TestDirectORMForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Testing\\TestDirectORMForm.cs", "ViewState": "AgIAAJEAAAAAAAAAAAAkwJoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T04:58:20.296Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "IDbConnection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DirectORM\\IDbConnection.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Data\\DirectORM\\IDbConnection.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DirectORM\\IDbConnection.cs", "RelativeToolTip": "InvoiceSystem\\Data\\DirectORM\\IDbConnection.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:02:08.563Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "DirectDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DirectORM\\DirectDbContext.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Data\\DirectORM\\DirectDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Data\\DirectORM\\DirectDbContext.cs", "RelativeToolTip": "InvoiceSystem\\Data\\DirectORM\\DirectDbContext.cs", "ViewState": "AgIAADEAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:02:06.132Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "SecureMainForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\SecureMainForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\SecureMainForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\SecureMainForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\SecureMainForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T04:59:42.763Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 42, "Title": "SimpleChartOfAccountsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimpleChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\SimpleChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\SimpleChartOfAccountsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\SimpleChartOfAccountsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:36.343Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "AddEditAccountForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T15:18:55.057Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 46, "Title": "AddEditAccountForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\AddEditAccountForm.cs", "ViewState": "AgIAAOQAAAAAAAAAAAAewAQBAABRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:46:35.806Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "ModernChartOfAccountsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\ModernChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\ModernChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\ModernChartOfAccountsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\ModernChartOfAccountsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:29.09Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "IncomeStatementReportForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\IncomeStatementReportForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\IncomeStatementReportForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\IncomeStatementReportForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\IncomeStatementReportForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:25.379Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "HierarchicalChartOfAccountsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\HierarchicalChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\HierarchicalChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\HierarchicalChartOfAccountsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\HierarchicalChartOfAccountsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:22.503Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "GeneralLedgerReportForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\GeneralLedgerReportForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\GeneralLedgerReportForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\GeneralLedgerReportForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\GeneralLedgerReportForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:20.374Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "CashFlowReportForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\CashFlowReportForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\CashFlowReportForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\CashFlowReportForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\CashFlowReportForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:05:15.882Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "ChartOfAccountsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\ChartOfAccountsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Accounting\\ChartOfAccountsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Accounting\\ChartOfAccountsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Accounting\\ChartOfAccountsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T03:02:33.561Z"}]}, {"DockedWidth": 176, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}]}]}]}