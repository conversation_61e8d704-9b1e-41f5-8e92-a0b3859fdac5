{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Blackbox\\Invoices\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\unitofmeasureservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\unitofmeasureservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\productsform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\productcategoriesform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\productcategoriesform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\productcategoriesform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\productcategoriesform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\updatestockform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\updatestockform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\products\\addeditproductcategoryform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\services\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\services\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\forms\\securemainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\forms\\securemainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|c:\\users\\<USER>\\blackbox\\invoices\\invoicesystem\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{283A5E9A-3544-6614-4CFD-89A2362C5050}|InvoiceSystem\\InvoiceSystem.csproj|solutionrelative:invoicesystem\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 154, "SelectedChildIndex": 13, "Children": [{"$type": "Bookmark", "Name": "ST:2:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:1966167525:0:{bf84ea66-d821-4dba-b1b1-2777d8574775}"}, {"$type": "Bookmark", "Name": "ST:1:0:{fabf9319-47eb-497e-b8f6-d9f73fba5f55}"}, {"$type": "Bookmark", "Name": "ST:0:0:{a958a09d-0df3-46ec-b584-ff0469ce7631}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:254354193:0:{71f361cc-493f-47c0-923f-f2570b6f8618}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "AddEditProductForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T01:56:48.96Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProductCategoriesForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T23:25:08.911Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProductsForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:55:54.537Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ProductCategoriesForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\ProductCategoriesForm.cs", "ViewState": "AgIAAHoAAAAAAAAAAAAYwI8AAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:55:33.154Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AddEditProductForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductForm.cs", "ViewState": "AgIAAGUAAAAAAAAAAAAYwHoAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:55:26.277Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProductsForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\ProductsForm.cs", "ViewState": "AgIAACEAAAAAAAAAAAAYwDYAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:55:39.492Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "UnitOfMeasureService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\UnitOfMeasureService.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\UnitOfMeasureService.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\UnitOfMeasureService.cs", "RelativeToolTip": "InvoiceSystem\\Services\\UnitOfMeasureService.cs", "ViewState": "AgIAAOUAAAAAAAAAAAD4vwkBAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T23:04:06.251Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "UpdateStockForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\UpdateStockForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\UpdateStockForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\UpdateStockForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\UpdateStockForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T01:56:57.679Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AddEditProductCategoryForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs [Design]", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T01:56:51.129Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 9, "Title": "AddEditProductCategoryForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\Products\\AddEditProductCategoryForm.cs", "ViewState": "AgIAAFoBAAAAAAAAAAAswHEBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T23:24:34.735Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ProductService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\ProductService.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Services\\ProductService.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Services\\ProductService.cs", "RelativeToolTip": "InvoiceSystem\\Services\\ProductService.cs", "ViewState": "AgIAAAgBAAAAAAAAAAAWwB8BAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T23:04:12.265Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "SecureMainForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\SecureMainForm.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Forms\\SecureMainForm.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Forms\\SecureMainForm.cs", "RelativeToolTip": "InvoiceSystem\\Forms\\SecureMainForm.cs", "ViewState": "AgIAAAwFAAAAAAAAAAAWwCMFAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T23:04:01.282Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Form1.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Form1.cs", "RelativeDocumentMoniker": "InvoiceSystem\\Form1.cs", "ToolTip": "C:\\Users\\<USER>\\Blackbox\\Invoices\\InvoiceSystem\\Form1.cs", "RelativeToolTip": "InvoiceSystem\\Form1.cs", "ViewState": "AgIAABIAAAAAAAAAAAAWwCkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T22:55:10.036Z", "EditorCaption": ""}]}, {"DockedWidth": 176, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}]}]}]}