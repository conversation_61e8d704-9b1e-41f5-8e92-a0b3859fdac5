using System.ComponentModel.DataAnnotations;

namespace InvoiceSystem.Models
{
    /// <summary>
    /// نموذج المنتجات المحسن
    /// </summary>
    public class Product
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "السعر مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "السعر يجب أن يكون أكبر من صفر")]
        public decimal Price { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal Cost { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي صفر")]
        public int Stock { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "حد المخزون المنخفض يجب أن يكون أكبر من أو يساوي صفر")]
        public int LowStockThreshold { get; set; } = 10;

        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string? Barcode { get; set; }

        public int? CategoryId { get; set; }
        public int? UnitOfMeasureId { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }

        // Navigation Properties
        public ProductCategory? Category { get; set; }
        public UnitOfMeasure? UnitOfMeasure { get; set; }

        // Computed Properties
        public string DisplayName => $"{Name} - {Price:C}";
        public string StockStatus => Stock > LowStockThreshold ? "متوفر" : Stock > 0 ? "مخزون منخفض" : "نفد المخزون";
        public bool IsLowStock => Stock <= LowStockThreshold && Stock > 0;
        public bool IsOutOfStock => Stock <= 0;
        public decimal ProfitMargin => Price > 0 ? ((Price - Cost) / Price) * 100 : 0;
        public decimal TotalValue => Price * Stock;
        public string UnitName => UnitOfMeasure?.Name ?? "قطعة";
        public string CategoryName => Category?.Name ?? "غير محدد";
    }

    /// <summary>
    /// تصنيفات المنتجات
    /// </summary>
    public class ProductCategory
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم التصنيف مطلوب")]
        [StringLength(100, ErrorMessage = "اسم التصنيف يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(300, ErrorMessage = "الوصف يجب أن يكون أقل من 300 حرف")]
        public string Description { get; set; } = string.Empty;

        [StringLength(7, ErrorMessage = "لون غير صحيح")]
        public string Color { get; set; } = "#007ACC";

        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }

        // Navigation Properties
        public List<Product> Products { get; set; } = new List<Product>();

        // Computed Properties
        public int ProductCount => Products?.Count ?? 0;
    }

    /// <summary>
    /// وحدات القياس
    /// </summary>
    public class UnitOfMeasure
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الوحدة مطلوب")]
        [StringLength(50, ErrorMessage = "اسم الوحدة يجب أن يكون أقل من 50 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(10, ErrorMessage = "الرمز يجب أن يكون أقل من 10 أحرف")]
        public string Symbol { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الوصف يجب أن يكون أقل من 200 حرف")]
        public string Description { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public List<Product> Products { get; set; } = new List<Product>();

        // Computed Properties
        public string DisplayName => !string.IsNullOrEmpty(Symbol) ? $"{Name} ({Symbol})" : Name;
        public int ProductCount => Products?.Count ?? 0;
    }

    /// <summary>
    /// حركات المنتجات (مبيعات ومردودات)
    /// </summary>
    public class ProductMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public ProductMovementType Type { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalAmount { get; set; }
        public string? Reference { get; set; } // رقم الفاتورة أو المرجع
        public string? Notes { get; set; }
        public DateTime MovementDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation Properties
        public Product? Product { get; set; }

        // Computed Properties
        public string TypeText => Type switch
        {
            ProductMovementType.Sale => "مبيعات",
            ProductMovementType.SaleReturn => "مردود مبيعات",
            ProductMovementType.Purchase => "مشتريات",
            ProductMovementType.PurchaseReturn => "مردود مشتريات",
            ProductMovementType.StockAdjustment => "تعديل مخزون",
            ProductMovementType.Transfer => "تحويل",
            _ => "غير محدد"
        };
    }

    /// <summary>
    /// أنواع حركات المنتجات
    /// </summary>
    public enum ProductMovementType
    {
        Sale = 1,           // مبيعات
        SaleReturn = 2,     // مردود مبيعات
        Purchase = 3,       // مشتريات
        PurchaseReturn = 4, // مردود مشتريات
        StockAdjustment = 5,// تعديل مخزون
        Transfer = 6        // تحويل
    }
}
